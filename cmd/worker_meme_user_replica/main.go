package main

import (
	"fmt"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	natsClient "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
	// "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task"
)

func main() {
	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initializer.Gorm()
	initializer.DBList()

	initializer.InitNatsMeme()

	memeNats := global.GVA_NATS_MEME
	err := memeNats.EnsureReadonlyStreamExists(natsClient.DexUserStream)
	if err != nil {
		panic(fmt.Sprintf("Failed to ensure readonly stream %s exists: %v", natsClient.DexUserStream, err))
	}

	go runSyncUserNatsWorker()
	select {}
}

func runSyncUserNatsWorker() error {
	logger := global.GVA_LOG

	nc := global.GVA_NATS_MEME

	// 删除可能存在的旧消费者（无论类型），避免类型冲突
	consumerInfo, err := nc.ConsumerInfo(natsClient.DexUserStream, natsClient.AgentSyncUserConsumerReplica)
	if err == nil && consumerInfo != nil {
		logger.Info("Deleting existing consumer to recreate as standard consumer",
			zap.String("consumer", natsClient.AgentSyncUserConsumerReplica),
			zap.String("deliverGroup", consumerInfo.Config.DeliverGroup))
		if deleteErr := nc.DeleteConsumer(natsClient.DexUserStream, natsClient.AgentSyncUserConsumerReplica); deleteErr != nil {
			logger.Warn("Failed to delete existing consumer, will try to create new one anyway", zap.Error(deleteErr))
		} else {
			logger.Info("Successfully deleted existing consumer")
		}
	}

	// subjects := map[string]func(msg *nats.Msg) error{
	// 	string(task.UserNewWalletSubject): task.ConsumeUserSyncInfoEvent,
	// }

	subject := "dex.user.>"
	opts := []nats.SubOpt{
		nats.BindStream(natsClient.DexUserStream),
		nats.Durable(natsClient.AgentSyncUserConsumerReplica),
		nats.ManualAck(),
		nats.MaxDeliver(10),
		nats.BackOff([]time.Duration{500 * time.Millisecond, time.Second, 3 * time.Second, 5 * time.Second}),
		nats.DeliverAll(),
	}
	_, err = nc.SubscribeJS(subject, func(msg *nats.Msg) {
		go func() {
			fmt.Printf("[%s] 收到消息 - 主题: %s, 数据: %s\n",
				subject, msg.Subject, string(msg.Data))
			// start := time.Now()
			// defer func() {
			// 	elapsed := time.Since(start)
			// 	logger.Info("Worker process completed: ", zap.String("Subject", msg.Subject), zap.Duration("duration", elapsed))
			// }()

			// handler, ok := subjects[msg.Subject]
			// if !ok || handler == nil {
			// 	logger.Error("failed: Worker handler not found:", zap.String("Subject", msg.Subject))
			// 	msg.Ack()
			// 	return
			// }

			// err := handler(msg)
			// if err != nil {
			// 	logger.Error("Worker Consume subjectfailed:", zap.String("Subject", msg.Subject), zap.Error(err), zap.Any("Data", msg.Data))
			// 	msg.Nak()
			// } else {
			// 	msg.Ack()
			// }
		}()
	}, opts...)
	if err != nil {
		logger.Error("Could not subscribe to subject:", zap.String("Subject", subject), zap.Error(err))
		return err
	}
	logger.Info("Worker started, listening on JetStream subjects.")
	return nil
}
