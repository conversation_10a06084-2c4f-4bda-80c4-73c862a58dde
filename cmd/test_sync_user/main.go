package main

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	natsjs "github.com/nats-io/nats.go"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	natsClient "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task"
)

func main() {
	// 初始化配置
	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	zap.ReplaceGlobals(global.GVA_LOG)

	// 初始化 NATS Meme 客户端
	initializer.InitNatsMeme()

	nats := global.GVA_NATS_MEME
	if nats == nil {
		global.GVA_LOG.Fatal("NATS Meme client not initialized")
	}

	// 确保流存在（如果不存在则创建）
	_, err := nats.AddStream(&natsjs.StreamConfig{
		Name:     natsClient.DexUserStream,
		Subjects: []string{"dex.user.>"},
		Storage:  natsjs.FileStorage,
	})
	if err != nil {
		global.GVA_LOG.Fatal("Failed to ensure stream exists", zap.Error(err))
	}

	// 创建测试事件数据
	testEvent := task.UserNewWalletEvent{
		UserID: uuid.New(),
		Wallets: []task.UserWalletInfo{
			{
				ID:              uuid.New().String(),
				Chain:           model.ChainSolana,
				WalletAddress:   "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU",
				WalletAccountID: uuid.New(),
				WalletID:        uuid.New(),
				CreatedAt:       time.Now().Format(time.RFC3339),
			},
			{
				ID:              uuid.New().String(),
				Chain:           model.ChainEvm,
				WalletAddress:   "0x742d35Cc6634C0532925a3b844Bc9e7595f0bEb",
				WalletAccountID: uuid.New(),
				WalletID:        uuid.New(),
				CreatedAt:       time.Now().Format(time.RFC3339),
			},
		},
	}

	// 序列化为 JSON
	eventData, err := json.Marshal(testEvent)
	if err != nil {
		global.GVA_LOG.Fatal("Failed to marshal event", zap.Error(err))
	}

	// 发布到 JetStream
	subject := string(task.UserNewWalletSubject)
	ack, err := nats.PublishJS(subject, eventData)
	if err != nil {
		global.GVA_LOG.Fatal("Failed to publish message", zap.Error(err))
	}

	global.GVA_LOG.Info("Successfully published test message",
		zap.String("subject", subject),
		zap.String("stream", ack.Stream),
		zap.Uint64("sequence", ack.Sequence),
		zap.String("user_id", testEvent.UserID.String()),
		zap.Int("wallets_count", len(testEvent.Wallets)),
	)

	fmt.Printf("✓ 成功发布测试消息到主题: %s\n", subject)
	fmt.Printf("  Stream: %s\n", ack.Stream)
	fmt.Printf("  Sequence: %d\n", ack.Sequence)
	fmt.Printf("  UserID: %s\n", testEvent.UserID.String())
	fmt.Printf("  Wallets: %d\n", len(testEvent.Wallets))
}
