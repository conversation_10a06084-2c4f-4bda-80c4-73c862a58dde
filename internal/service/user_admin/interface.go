package user_admin

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
)

// AdminUserServiceInterface defines the interface for admin user operations
type AdminUserServiceInterface interface {
	GetUserList(ctx context.Context, input gql_model.GetUserListInput) (*gql_model.UserListResponse, error)
	GetUserInfo(ctx context.Context, input gql_model.GetUserInfoInput) (*gql_model.UserInfoResponse, error)
	ManualLevelUpgrade(ctx context.Context, input gql_model.ManualLevelUpgradeInput) (*gql_model.ManualLevelUpgradeResponse, error)
}
