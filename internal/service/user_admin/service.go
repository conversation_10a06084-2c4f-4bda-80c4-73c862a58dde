package user_admin

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// AdminUserService handles admin user operations
type AdminUserService struct{}

// NewAdminUserService creates a new AdminUserService
func NewAdminUserService() AdminUserServiceInterface {
	return &AdminUserService{}
}

// GetUserList retrieves a paginated list of users with optional filtering
func (s *AdminUserService) GetUserList(ctx context.Context, input gql_model.GetUserListInput) (*gql_model.UserListResponse, error) {
	// Set default pagination values
	page := 1
	pageSize := 10
	if input.Page != nil {
		page = *input.Page
	}
	if input.PageSize != nil {
		pageSize = *input.PageSize
	}

	// Set default sort values
	sortBy := "created_at"
	sortOrder := "DESC"
	if input.SortBy != nil {
		sortBy = *input.SortBy
	}
	if input.SortOrder != nil {
		sortOrder = *input.SortOrder
	}

	// Calculate offset
	offset := (page - 1) * pageSize

	// Build query
	query := global.GVA_DB.WithContext(ctx).Model(&model.User{}).
		Preload("AgentLevel").
		Preload("ReferralSnapshot")

	// Apply filters
	if input.UserID != nil && *input.UserID != "" {
		userID, err := uuid.Parse(*input.UserID)
		if err != nil {
			return nil, fmt.Errorf("invalid user ID: %w", err)
		}
		query = query.Where("id = ?", userID)
	}

	if input.Email != nil && *input.Email != "" {
		query = query.Where("email ILIKE ?", "%"+*input.Email+"%")
	}

	if input.InvitationCode != nil && *input.InvitationCode != "" {
		query = query.Where("invitation_code ILIKE ?", "%"+*input.InvitationCode+"%")
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		global.GVA_LOG.Error("Failed to count users", zap.Error(err))
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	// Get users with pagination
	var users []model.User
	orderClause := fmt.Sprintf("%s %s", sortBy, sortOrder)
	if err := query.Order(orderClause).Limit(pageSize).Offset(offset).Find(&users).Error; err != nil {
		global.GVA_LOG.Error("Failed to get users", zap.Error(err))
		return nil, fmt.Errorf("failed to get users: %w", err)
	}

	// Convert to GraphQL response format
	userListInfos := make([]*gql_model.UserListInfo, 0, len(users))
	for _, user := range users {
		userListInfo, err := s.convertUserToUserListInfo(ctx, user)
		if err != nil {
			global.GVA_LOG.Warn("Failed to convert user to UserListInfo",
				zap.String("userID", user.ID.String()),
				zap.Error(err))
			continue
		}
		userListInfos = append(userListInfos, userListInfo)
	}

	// Calculate total pages
	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))

	return &gql_model.UserListResponse{
		Users:      userListInfos,
		Total:      int(total),
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// GetUserInfo retrieves detailed information about a specific user
func (s *AdminUserService) GetUserInfo(ctx context.Context, input gql_model.GetUserInfoInput) (*gql_model.UserInfoResponse, error) {
	userID, err := uuid.Parse(input.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// Get user with related data
	var user model.User
	err = global.GVA_DB.WithContext(ctx).
		Preload("AgentLevel").
		Preload("ReferralSnapshot").
		First(&user, "id = ?", userID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		global.GVA_LOG.Error("Failed to get user", zap.Error(err))
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Check if user is an infinite agent
	var infiniteAgentConfig *gql_model.InfiniteAgentConfig
	var isInfiniteAgent bool

	var config model.InfiniteAgentConfig
	err = global.GVA_DB.WithContext(ctx).
		Where("user_id = ? AND status = ?", userID, "ACTIVE").
		First(&config).Error
	if err == nil {
		isInfiniteAgent = true
		infiniteAgentConfig = s.convertInfiniteAgentConfigToGQL(config)
	} else if err != gorm.ErrRecordNotFound {
		global.GVA_LOG.Warn("Failed to check infinite agent status",
			zap.String("userID", userID.String()),
			zap.Error(err))
	}

	// Convert user to UserListInfo format
	userListInfo, err := s.convertUserToUserListInfo(ctx, user)
	if err != nil {
		global.GVA_LOG.Warn("Failed to convert user to UserListInfo",
			zap.String("userID", userID.String()),
			zap.Error(err))
		// Create a basic UserListInfo if conversion fails
		var referralSnapshot *gql_model.ReferralSnapshotFull
		if user.ReferralSnapshot != nil {
			referralSnapshot = s.convertReferralSnapshotToGQL(*user.ReferralSnapshot)
		}

		userListInfo = &gql_model.UserListInfo{
			UserID:           userID.String(),
			UserLevel:        user.AgentLevel.Name,
			WalletAddresses:  []string{},
			User:             s.convertUserToGQL(user, user.AgentLevel),
			UserWallets:      []*gql_model.UserWallet{},
			ReferralSnapshot: referralSnapshot,
			IsInfiniteAgent:  isInfiniteAgent,
		}
	}

	return &gql_model.UserInfoResponse{
		UserID:              userID.String(),
		IsInfiniteAgent:     isInfiniteAgent,
		InfiniteAgentConfig: infiniteAgentConfig,
		Users:               []*gql_model.UserListInfo{userListInfo},
	}, nil
}

// convertUserToUserListInfo converts a model.User to gql_model.UserListInfo
func (s *AdminUserService) convertUserToUserListInfo(ctx context.Context, user model.User) (*gql_model.UserListInfo, error) {
	// Get user wallets
	var wallets []model.UserWallet
	err := global.GVA_DB.WithContext(ctx).
		Where("user_id = ?", user.ID).
		Find(&wallets).Error
	if err != nil {
		global.GVA_LOG.Warn("Failed to get user wallets",
			zap.String("userID", user.ID.String()),
			zap.Error(err))
	}

	// Convert wallets to string slice
	walletAddresses := make([]string, 0, len(wallets))
	userWallets := make([]*gql_model.UserWallet, 0, len(wallets))
	for _, wallet := range wallets {
		walletAddresses = append(walletAddresses, wallet.WalletAddress)
		userWallets = append(userWallets, &gql_model.UserWallet{
			ID:            wallet.ID.String(),
			UserID:        wallet.UserID.String(),
			WalletAddress: wallet.WalletAddress,
			WalletType:    string(wallet.Chain),
			IsActive:      wallet.DeletedAt.Time.IsZero(),
			CreatedAt:     wallet.CreatedAt,
			UpdatedAt:     wallet.UpdatedAt,
		})
	}

	// Convert user to GraphQL format
	gqlUser := &gql_model.User{
		ID:             user.ID.String(),
		Email:          user.Email,
		InvitationCode: user.InvitationCode,
		CreatedAt:      user.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:      user.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
		AgentLevelID:   int(user.AgentLevelID),
		AgentLevel:     s.convertAgentLevelToGQL(user.AgentLevel),
	}

	if user.DeletedAt.Valid {
		deletedAt := user.DeletedAt.Time.Format("2006-01-02T15:04:05Z07:00")
		gqlUser.DeletedAt = &deletedAt
	}

	if user.LevelGracePeriodStartedAt != nil {
		gracePeriod := user.LevelGracePeriodStartedAt.Format("2006-01-02T15:04:05Z07:00")
		gqlUser.LevelGracePeriodStartedAt = &gracePeriod
	}

	if user.LevelUpgradedAt != nil {
		levelUpgraded := user.LevelUpgradedAt.Format("2006-01-02T15:04:05Z07:00")
		gqlUser.LevelUpgradedAt = &levelUpgraded
	}

	if user.FirstTransactionAt != nil {
		firstTransaction := user.FirstTransactionAt.Format("2006-01-02T15:04:05Z07:00")
		gqlUser.FirstTransactionAt = &firstTransaction
	}

	// Convert referral snapshot if exists
	var referralSnapshot *gql_model.ReferralSnapshotFull
	if user.ReferralSnapshot != nil {
		referralSnapshot = s.convertReferralSnapshotToGQL(*user.ReferralSnapshot)
	}

	// Check if user is an infinite agent
	var isInfiniteAgent bool
	var infiniteAgentConfig model.InfiniteAgentConfig
	err = global.GVA_DB.WithContext(ctx).
		Where("user_id = ? AND status = ?", user.ID, "ACTIVE").
		First(&infiniteAgentConfig).Error
	if err == nil {
		isInfiniteAgent = true
	} else if err != gorm.ErrRecordNotFound {
		global.GVA_LOG.Warn("Failed to check infinite agent status",
			zap.String("userID", user.ID.String()),
			zap.Error(err))
	}

	return &gql_model.UserListInfo{
		UserID:           user.ID.String(),
		UserLevel:        user.AgentLevel.Name,
		WalletAddresses:  walletAddresses,
		User:             gqlUser,
		UserWallets:      userWallets,
		ReferralSnapshot: referralSnapshot,
		IsInfiniteAgent:  isInfiniteAgent,
	}, nil
}

// convertAgentLevelToGQL converts model.AgentLevel to gql_model.AgentLevel
func (s *AdminUserService) convertAgentLevelToGQL(level model.AgentLevel) *gql_model.AgentLevel {
	return &gql_model.AgentLevel{
		ID:                      int(level.ID),
		Name:                    level.Name,
		MemeVolumeThreshold:     level.MemeVolumeThreshold.InexactFloat64(),
		ContractVolumeThreshold: level.ContractVolumeThreshold.InexactFloat64(),
		MemeFeeRate:             level.MemeFeeRate.InexactFloat64(),
		TakerFeeRate:            level.TakerFeeRate.InexactFloat64(),
		MakerFeeRate:            level.MakerFeeRate.InexactFloat64(),
		DirectCommissionRate:    level.DirectCommissionRate.InexactFloat64(),
		IndirectCommissionRate:  level.IndirectCommissionRate.InexactFloat64(),
		ExtendedCommissionRate:  level.ExtendedCommissionRate.InexactFloat64(),
		MemeFeeRebate:           level.MemeFeeRebate.InexactFloat64(),
	}
}

// convertReferralSnapshotToGQL converts model.ReferralSnapshot to gql_model.ReferralSnapshotFull
func (s *AdminUserService) convertReferralSnapshotToGQL(snapshot model.ReferralSnapshot) *gql_model.ReferralSnapshotFull {
	return &gql_model.ReferralSnapshotFull{
		UserID:                   snapshot.UserID.String(),
		DirectCount:              snapshot.DirectCount,
		TotalDownlineCount:       snapshot.TotalDownlineCount,
		TotalVolumeUsd:           snapshot.TotalVolumeUSD.InexactFloat64(),
		TotalRewardsDistributed:  snapshot.TotalRewardsDistributed.InexactFloat64(),
		TradingUserCount:         snapshot.TradingUserCount,
		TotalPerpsVolumeUsd:      snapshot.TotalPerpsVolumeUSD.InexactFloat64(),
		TotalPerpsFees:           snapshot.TotalPerpsFees.InexactFloat64(),
		TotalPerpsFeesPaid:       snapshot.TotalPerpsFeesPaid.InexactFloat64(),
		TotalPerpsFeesUnpaid:     snapshot.TotalPerpsFeesUnPaid.InexactFloat64(),
		TotalPerpsTradesCount:    int(snapshot.TotalPerpsTradesCount),
		TotalMemeVolumeUsd:       snapshot.TotalMemeVolumeUSD.InexactFloat64(),
		TotalMemeFees:            snapshot.TotalMemeFees.InexactFloat64(),
		TotalMemeFeesPaid:        snapshot.TotalMemeFeesPaid.InexactFloat64(),
		TotalMemeFeesUnpaid:      snapshot.TotalMemeFeesUnPaid.InexactFloat64(),
		TotalMemeTradesCount:     int(snapshot.TotalMemeTradesCount),
		TotalCommissionEarnedUsd: snapshot.TotalCommissionEarnedUSD.InexactFloat64(),
		ClaimedCommissionUsd:     snapshot.ClaimedCommissionUSD.InexactFloat64(),
		UnclaimedCommissionUsd:   snapshot.UnclaimedCommissionUSD.InexactFloat64(),
		TotalCashbackEarnedUsd:   snapshot.TotalCashbackEarnedUSD.InexactFloat64(),
		ClaimedCashbackUsd:       snapshot.ClaimedCashbackUSD.InexactFloat64(),
		UnclaimedCashbackUsd:     snapshot.UnclaimedCashbackUSD.InexactFloat64(),
		L1UplineID:               s.convertUUIDPointerToString(snapshot.L1UplineID),
		L2UplineID:               s.convertUUIDPointerToString(snapshot.L2UplineID),
		L3UplineID:               s.convertUUIDPointerToString(snapshot.L3UplineID),
	}
}

// convertInfiniteAgentConfigToGQL converts model.InfiniteAgentConfig to gql_model.InfiniteAgentConfig
func (s *AdminUserService) convertInfiniteAgentConfigToGQL(config model.InfiniteAgentConfig) *gql_model.InfiniteAgentConfig {
	return &gql_model.InfiniteAgentConfig{
		ID:                             config.ID.String(),
		UserID:                         config.UserID.String(),
		CommissionRateN:                config.CommissionRateN.InexactFloat64(),
		TotalNetFeeUsd:                 config.TotalNetFeeUSD.String(),
		TotalStandardCommissionPaidUsd: config.TotalStandardCommissionPaidUSD.String(),
		FinalCommissionAmountUsd:       config.FinalCommissionAmountUSD.String(),
		MemeTotalFeeUsd:                config.MemeTotalFeeUSD.String(),
		MemePaidCommissionUsd:          config.MemePaidCommissionUSD.String(),
		MemeNetFeeUsd:                  config.MemeNetFeeUSD.String(),
		MemeActivityCashbackUsd:        config.MemeActivityCashbackUSD.String(),
		ContractTotalFeeUsd:            config.ContractTotalFeeUSD.String(),
		ContractPaidCommissionUsd:      config.ContractPaidCommissionUSD.String(),
		ContractNetFeeUsd:              config.ContractNetFeeUSD.String(),
		Status:                         config.Status,
		CreatedAt:                      config.CreatedAt,
		UpdatedAt:                      config.UpdatedAt,
	}
}

// ManualLevelUpgrade manually upgrades a user's level (admin operation)
func (s *AdminUserService) ManualLevelUpgrade(ctx context.Context, input gql_model.ManualLevelUpgradeInput) (*gql_model.ManualLevelUpgradeResponse, error) {
	// Get admin user ID from context
	// adminUserID := s.GetUserIDFromContext(ctx)
	// if adminUserID == uuid.Nil {
	// 	return nil, fmt.Errorf("unauthorized: admin user ID not found in context")
	// }

	// Parse target user ID
	targetUserID, err := uuid.Parse(input.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// Validate target level (must be between 1 and 7)
	if input.TargetLevelID < 1 || input.TargetLevelID > 7 {
		return nil, fmt.Errorf("invalid target level: level must be between 1 and 7, got %d", input.TargetLevelID)
	}

	// Get target user with current level information
	var targetUser model.User
	err = global.GVA_DB.WithContext(ctx).
		Preload("AgentLevel").
		First(&targetUser, "id = ?", targetUserID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		global.GVA_LOG.Error("Failed to get target user", zap.Error(err))
		return nil, fmt.Errorf("failed to get target user: %w", err)
	}

	// Check if user is already at the target level
	if targetUser.AgentLevelID == uint(input.TargetLevelID) {
		return nil, fmt.Errorf("user is already at level %d", input.TargetLevelID)
	}

	// Get target level information
	var targetLevel model.AgentLevel
	err = global.GVA_DB.WithContext(ctx).
		First(&targetLevel, "id = ?", input.TargetLevelID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("target level %d not found", input.TargetLevelID)
		}
		global.GVA_LOG.Error("Failed to get target level", zap.Error(err))
		return nil, fmt.Errorf("failed to get target level: %w", err)
	}

	// Store previous level for response
	previousLevel := targetUser.AgentLevel

	// Prepare update data
	now := time.Now().UTC()
	upgradeReason := input.Reason
	upgradedBy := input.UpgradedBy

	// If no reason provided, set default
	if upgradeReason == nil {
		defaultReason := "Manual upgrade by admin"
		upgradeReason = &defaultReason
	}

	// If no upgraded by provided, set default
	if upgradedBy == nil {
		defaultUpgradedBy := "admin"
		upgradedBy = &defaultUpgradedBy
	}

	// Log the update attempt for debugging
	global.GVA_LOG.Info("Attempting to update user level",
		zap.String("targetUserID", targetUserID.String()),
		zap.Uint("currentLevel", targetUser.AgentLevelID),
		zap.Uint("targetLevel", targetLevel.ID),
		zap.String("reason", *upgradeReason),
		zap.String("upgradedBy", *upgradedBy))

	// Update user level and manual upgrade fields
	// Use direct WHERE clause instead of Model() to avoid potential issues
	err = global.GVA_DB.WithContext(ctx).
		Model(&model.User{}).
		Where("id = ?", targetUserID).
		Updates(map[string]interface{}{
			"agent_level_id":                targetLevel.ID,
			"is_manual_level_upgrade":       true,
			"manual_upgrade_reason":         upgradeReason,
			"manual_upgraded_at":            &now,
			"manual_upgraded_by":            upgradedBy,
			"level_upgraded_at":             &now,
			"level_grace_period_started_at": nil, // Clear grace period for manual upgrades
		}).Error

	if err != nil {
		global.GVA_LOG.Error("Failed to update user level",
			zap.String("targetUserID", targetUserID.String()),
			// zap.String("adminUserID", adminUserID.String()),
			zap.Int("targetLevelID", input.TargetLevelID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to update user level: %w", err)
	}

	// Verify the update was successful by re-querying the user
	var updatedUser model.User
	err = global.GVA_DB.WithContext(ctx).
		Preload("AgentLevel").
		First(&updatedUser, "id = ?", targetUserID).Error

	if err != nil {
		global.GVA_LOG.Error("Failed to verify update - user not found",
			zap.String("targetUserID", targetUserID.String()),
			zap.Error(err))
		return nil, fmt.Errorf("failed to verify update: %w", err)
	}

	global.GVA_LOG.Info("Update verification completed",
		zap.String("targetUserID", targetUserID.String()),
		zap.Uint("updatedLevel", updatedUser.AgentLevelID),
		zap.Bool("isManualUpgrade", updatedUser.IsManualLevelUpgrade))

	// Log successful manual upgrade
	global.GVA_LOG.Info("Manual level upgrade completed",
		zap.String("targetUserID", targetUserID.String()),
		// zap.String("adminUserID", adminUserID.String()),
		zap.Uint("previousLevel", previousLevel.ID),
		zap.Uint("newLevel", targetLevel.ID),
		zap.String("reason", *upgradeReason),
		zap.String("upgradedBy", *upgradedBy))

	// Convert to GraphQL response format
	response := &gql_model.ManualLevelUpgradeResponse{
		Success:         true,
		Message:         fmt.Sprintf("User successfully upgraded from level %d to level %d", previousLevel.ID, targetLevel.ID),
		User:            s.convertUserToGQL(updatedUser, targetLevel),
		PreviousLevel:   s.convertAgentLevelToGQL(previousLevel),
		NewLevel:        s.convertAgentLevelToGQL(targetLevel),
		UpgradedAt:      now,
		IsManualUpgrade: true,
	}

	return response, nil
}

// GetUserIDFromContext extracts user ID from context
func (s *AdminUserService) GetUserIDFromContext(ctx context.Context) uuid.UUID {
	userIdStr, _ := ctx.Value("userId").(string)
	userId := uuid.Nil
	if userIdStr != "" {
		userId, _ = uuid.Parse(userIdStr)
	}
	return userId
}

// convertUserToGQL converts model.User to gql_model.User with specific level
func (s *AdminUserService) convertUserToGQL(user model.User, level model.AgentLevel) *gql_model.User {
	gqlUser := &gql_model.User{
		ID:             user.ID.String(),
		Email:          user.Email,
		InvitationCode: user.InvitationCode,
		CreatedAt:      user.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:      user.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
		AgentLevelID:   int(user.AgentLevelID),
		AgentLevel:     s.convertAgentLevelToGQL(level),
	}

	if user.DeletedAt.Valid {
		deletedAt := user.DeletedAt.Time.Format("2006-01-02T15:04:05Z07:00")
		gqlUser.DeletedAt = &deletedAt
	}

	if user.LevelGracePeriodStartedAt != nil {
		gracePeriod := user.LevelGracePeriodStartedAt.Format("2006-01-02T15:04:05Z07:00")
		gqlUser.LevelGracePeriodStartedAt = &gracePeriod
	}

	if user.LevelUpgradedAt != nil {
		levelUpgraded := user.LevelUpgradedAt.Format("2006-01-02T15:04:05Z07:00")
		gqlUser.LevelUpgradedAt = &levelUpgraded
	}

	if user.FirstTransactionAt != nil {
		firstTransaction := user.FirstTransactionAt.Format("2006-01-02T15:04:05Z07:00")
		gqlUser.FirstTransactionAt = &firstTransaction
	}

	return gqlUser
}

// convertUUIDPointerToString converts *uuid.UUID to *string
func (s *AdminUserService) convertUUIDPointerToString(uuidPtr *uuid.UUID) *string {
	if uuidPtr == nil {
		return nil
	}
	str := uuidPtr.String()
	return &str
}
