package agent_referral_admin

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// AdminAgentReferralServiceInterface defines the interface for admin agent referral operations
type AdminAgentReferralServiceInterface interface {
	// Infinite Agent Management
	CreateInfiniteAgent(ctx context.Context, input *CreateInfiniteAgentInput) (*model.InfiniteAgentConfig, error)
	GetInfiniteAgentById(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentConfig, error)
	GetAllInfiniteAgents(ctx context.Context, input *GetInfiniteAgentsInput) (*GetInfiniteAgentsResponse, error)
	UpdateInfiniteAgent(ctx context.Context, input *UpdateInfiniteAgentInput) (*model.InfiniteAgentConfig, error)
	DeleteInfiniteAgent(ctx context.Context, id uuid.UUID) error

	// Agent Level Management
	GetAllAgentLevels(ctx context.Context) ([]*AgentLevelWithStats, error)
	UpdateAgentLevelCommissionRates(ctx context.Context, input *UpdateCommissionRatesInput) (*model.AgentLevel, error)

	// User Management
	SearchUsersByInvitationCode(ctx context.Context, invitationCode string) ([]*model.User, error)
	GetUserReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error)
	GetUsersByAgentLevel(ctx context.Context, input *GetUsersByLevelInput) (*GetUsersByLevelResponse, error)

	// Referral Tree Management
	GetAllReferralTrees(ctx context.Context, input *GetReferralTreesInput) (*GetReferralTreesResponse, error)
	CreateReferralTreeSnapshot(ctx context.Context, input *CreateTreeSnapshotInput) (*model.InfiniteAgentReferralTree, error)
	GetReferralTreeById(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentReferralTree, error)
	DeleteReferralTree(ctx context.Context, id uuid.UUID) error

	// Analytics and Statistics
	GetAgentReferralStats(ctx context.Context, startDate, endDate time.Time) (*AgentReferralStatsResponse, error)
	GetCommissionDistributionStats(ctx context.Context, startDate, endDate time.Time) (*CommissionDistributionStatsResponse, error)
	GetTopPerformingAgents(ctx context.Context, input *GetTopAgentsInput) ([]*TopAgentResponse, error)

	// System Operations
	RecalculateAllReferralSnapshots(ctx context.Context) (*SystemOperationResult, error)
	RecalculateAllInfiniteAgentCommissions(ctx context.Context) (*SystemOperationResult, error)
	SyncReferralTreeData(ctx context.Context) (*SystemOperationResult, error)
}

// Input/Output Types

// CreateInfiniteAgentInput represents input for creating an infinite agent
type CreateInfiniteAgentInput struct {
	UserID          uuid.UUID `json:"user_id" validate:"required"`
	CommissionRateN float64   `json:"commission_rate_n" validate:"required,min=0,max=1"`
	Status          string    `json:"status" validate:"required,oneof=ACTIVE INACTIVE SUSPENDED"`
}

// UpdateInfiniteAgentInput represents input for updating an infinite agent
type UpdateInfiniteAgentInput struct {
	ID              uuid.UUID `json:"id" validate:"required"`
	CommissionRateN *float64  `json:"commission_rate_n,omitempty" validate:"omitempty,min=0,max=1"`
	Status          *string   `json:"status,omitempty" validate:"omitempty,oneof=ACTIVE INACTIVE SUSPENDED"`
}

// GetInfiniteAgentsInput represents input for getting infinite agents with pagination
type GetInfiniteAgentsInput struct {
	Page      int    `json:"page" validate:"min=1"`
	PageSize  int    `json:"page_size" validate:"min=1,max=100"`
	Status    string `json:"status,omitempty"`
	SortBy    string `json:"sort_by,omitempty"`
	SortOrder string `json:"sort_order,omitempty" validate:"omitempty,oneof=ASC DESC"`
}

// GetInfiniteAgentsResponse represents response for getting infinite agents
type GetInfiniteAgentsResponse struct {
	Agents     []*model.InfiniteAgentConfig `json:"agents"`
	Total      int                          `json:"total"`
	Page       int                          `json:"page"`
	PageSize   int                          `json:"page_size"`
	TotalPages int                          `json:"total_pages"`
}

// UpdateCommissionRatesInput represents input for updating agent level commission rates
type UpdateCommissionRatesInput struct {
	LevelID                int     `json:"level_id" validate:"required"`
	DirectCommissionRate   float64 `json:"direct_commission_rate" validate:"required,min=0,max=1"`
	IndirectCommissionRate float64 `json:"indirect_commission_rate" validate:"required,min=0,max=1"`
	ExtendedCommissionRate float64 `json:"extended_commission_rate" validate:"required,min=0,max=1"`
}

// AgentLevelWithStats represents agent level with additional statistics
type AgentLevelWithStats struct {
	*model.AgentLevel
	UserCount       int     `json:"user_count"`
	TotalVolumeUSD  float64 `json:"total_volume_usd"`
	TotalCommission float64 `json:"total_commission"`
}

// GetUsersByLevelInput represents input for getting users by agent level
type GetUsersByLevelInput struct {
	LevelID   int    `json:"level_id" validate:"required"`
	Page      int    `json:"page" validate:"min=1"`
	PageSize  int    `json:"page_size" validate:"min=1,max=100"`
	SortBy    string `json:"sort_by,omitempty"`
	SortOrder string `json:"sort_order,omitempty" validate:"omitempty,oneof=ASC DESC"`
}

// GetUsersByLevelResponse represents response for getting users by level
type GetUsersByLevelResponse struct {
	Users      []*model.User `json:"users"`
	Total      int           `json:"total"`
	Page       int           `json:"page"`
	PageSize   int           `json:"page_size"`
	TotalPages int           `json:"total_pages"`
}

// GetReferralTreesInput represents input for getting referral trees
type GetReferralTreesInput struct {
	Page      int    `json:"page" validate:"min=1"`
	PageSize  int    `json:"page_size" validate:"min=1,max=100"`
	SortBy    string `json:"sort_by,omitempty"`
	SortOrder string `json:"sort_order,omitempty" validate:"omitempty,oneof=ASC DESC"`
}

// GetReferralTreesResponse represents response for getting referral trees
type GetReferralTreesResponse struct {
	Trees      []*model.InfiniteAgentReferralTree `json:"trees"`
	Total      int                                `json:"total"`
	Page       int                                `json:"page"`
	PageSize   int                                `json:"page_size"`
	TotalPages int                                `json:"total_pages"`
}

// CreateTreeSnapshotInput represents input for creating referral tree snapshot
type CreateTreeSnapshotInput struct {
	RootUserID uuid.UUID `json:"root_user_id" validate:"required"`
}

// AgentReferralStatsResponse represents agent referral statistics
type AgentReferralStatsResponse struct {
	TotalUsers          int     `json:"total_users"`
	TotalInfiniteAgents int     `json:"total_infinite_agents"`
	TotalVolumeUSD      float64 `json:"total_volume_usd"`
	TotalCommissionPaid float64 `json:"total_commission_paid"`
	ActiveReferralTrees int     `json:"active_referral_trees"`
	AverageTreeSize     float64 `json:"average_tree_size"`
}

// CommissionDistributionStatsResponse represents commission distribution statistics
type CommissionDistributionStatsResponse struct {
	DirectCommission   float64 `json:"direct_commission"`
	IndirectCommission float64 `json:"indirect_commission"`
	ExtendedCommission float64 `json:"extended_commission"`
	InfiniteCommission float64 `json:"infinite_commission"`
	TotalCommission    float64 `json:"total_commission"`
}

// GetTopAgentsInput represents input for getting top performing agents
type GetTopAgentsInput struct {
	Limit  int    `json:"limit" validate:"min=1,max=100"`
	SortBy string `json:"sort_by" validate:"required,oneof=COMMISSION_EARNED VOLUME_GENERATED REFERRAL_COUNT"`
}

// TopAgentResponse represents top performing agent information
type TopAgentResponse struct {
	UserID           uuid.UUID `json:"user_id"`
	InvitationCode   string    `json:"invitation_code"`
	Email            string    `json:"email"`
	AgentLevel       string    `json:"agent_level"`
	CommissionEarned float64   `json:"commission_earned"`
	VolumeGenerated  float64   `json:"volume_generated"`
	ReferralCount    int       `json:"referral_count"`
	IsInfiniteAgent  bool      `json:"is_infinite_agent"`
}

// SystemOperationResult represents result of system operations
type SystemOperationResult struct {
	Success        bool   `json:"success"`
	ProcessedCount int    `json:"processed_count"`
	ErrorCount     int    `json:"error_count"`
	Message        string `json:"message"`
}
