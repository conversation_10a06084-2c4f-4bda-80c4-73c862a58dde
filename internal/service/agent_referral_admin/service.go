package agent_referral_admin

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/agent_referral"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/agent_referral_admin"
)

// AdminAgentReferralService implements the AdminAgentReferralServiceInterface
type AdminAgentReferralService struct {
	adminRepo      agent_referral_admin.AdminAgentReferralRepositoryInterface
	invitationRepo *agent_referral.InvitationRepository
}

// NewAdminAgentReferralService creates a new admin service instance
func NewAdminAgentReferralService() AdminAgentReferralServiceInterface {
	return &AdminAgentReferralService{
		adminRepo:      agent_referral_admin.NewAdminAgentReferralRepository(),
		invitationRepo: &agent_referral.InvitationRepository{},
	}
}

// Infinite Agent Management

func (s *AdminAgentReferralService) CreateInfiniteAgent(ctx context.Context, input *CreateInfiniteAgentInput) (*model.InfiniteAgentConfig, error) {
	agent := &model.InfiniteAgentConfig{
		ID:              uuid.New(),
		UserID:          input.UserID,
		CommissionRateN: decimal.NewFromFloat(input.CommissionRateN),
		Status:          input.Status,
	}

	if err := s.adminRepo.CreateInfiniteAgent(ctx, agent); err != nil {
		return nil, fmt.Errorf("failed to create infinite agent: %w", err)
	}

	return agent, nil
}

func (s *AdminAgentReferralService) GetInfiniteAgentById(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentConfig, error) {
	return s.adminRepo.GetInfiniteAgentById(ctx, id)
}

func (s *AdminAgentReferralService) GetAllInfiniteAgents(ctx context.Context, input *GetInfiniteAgentsInput) (*GetInfiniteAgentsResponse, error) {
	// Set defaults
	if input.Page <= 0 {
		input.Page = 1
	}
	if input.PageSize <= 0 {
		input.PageSize = 10
	}
	if input.SortOrder == "" {
		input.SortOrder = "DESC"
	}

	offset := (input.Page - 1) * input.PageSize
	agents, total, err := s.adminRepo.GetAllInfiniteAgents(ctx, offset, input.PageSize, input.Status, input.SortBy, input.SortOrder)
	if err != nil {
		return nil, fmt.Errorf("failed to get infinite agents: %w", err)
	}

	totalPages := int(math.Ceil(float64(total) / float64(input.PageSize)))

	return &GetInfiniteAgentsResponse{
		Agents:     agents,
		Total:      total,
		Page:       input.Page,
		PageSize:   input.PageSize,
		TotalPages: totalPages,
	}, nil
}

func (s *AdminAgentReferralService) UpdateInfiniteAgent(ctx context.Context, input *UpdateInfiniteAgentInput) (*model.InfiniteAgentConfig, error) {
	agent, err := s.adminRepo.GetInfiniteAgentById(ctx, input.ID)
	if err != nil {
		return nil, fmt.Errorf("infinite agent not found: %w", err)
	}

	// Update fields if provided
	if input.CommissionRateN != nil {
		agent.CommissionRateN = decimal.NewFromFloat(*input.CommissionRateN)
	}
	if input.Status != nil {
		agent.Status = *input.Status
	}

	if err := s.adminRepo.UpdateInfiniteAgent(ctx, agent); err != nil {
		return nil, fmt.Errorf("failed to update infinite agent: %w", err)
	}

	return agent, nil
}

func (s *AdminAgentReferralService) DeleteInfiniteAgent(ctx context.Context, id uuid.UUID) error {
	return s.adminRepo.DeleteInfiniteAgent(ctx, id)
}

func (s *AdminAgentReferralService) GetAllAgentLevels(ctx context.Context) ([]*AgentLevelWithStats, error) {
	levels, err := s.adminRepo.GetAllAgentLevels(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get agent levels: %w", err)
	}

	var levelsWithStats []*AgentLevelWithStats
	for _, level := range levels {
		// userCount, totalVolume, totalCommission, err := s.adminRepo.GetAgentLevelStats(ctx, int(level.ID))
		// if err != nil {
		// 	// Log error but continue with zero stats
		// 	userCount, totalVolume, totalCommission = 0, 0, 0
		// }

		levelWithStats := &AgentLevelWithStats{
			AgentLevel: level,
			// UserCount:       userCount,
			// TotalVolumeUSD:  totalVolume,
			// TotalCommission: totalCommission,
			UserCount:       0,
			TotalVolumeUSD:  0,
			TotalCommission: 0,
		}
		levelsWithStats = append(levelsWithStats, levelWithStats)
	}

	return levelsWithStats, nil

}

func (s *AdminAgentReferralService) UpdateAgentLevelCommissionRates(ctx context.Context, input *UpdateCommissionRatesInput) (*model.AgentLevel, error) {
	levels, err := s.adminRepo.GetAllAgentLevels(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get agent levels: %w", err)
	}

	var targetLevel *model.AgentLevel
	for _, level := range levels {
		if int(level.ID) == input.LevelID {
			targetLevel = level
			break
		}
	}

	if targetLevel == nil {
		return nil, fmt.Errorf("agent level with ID %d not found", input.LevelID)
	}

	// Update commission rates
	targetLevel.DirectCommissionRate = decimal.NewFromFloat(input.DirectCommissionRate)
	targetLevel.IndirectCommissionRate = decimal.NewFromFloat(input.IndirectCommissionRate)
	targetLevel.ExtendedCommissionRate = decimal.NewFromFloat(input.ExtendedCommissionRate)

	if err := s.adminRepo.UpdateAgentLevel(ctx, targetLevel); err != nil {
		return nil, fmt.Errorf("failed to update agent level: %w", err)
	}

	return targetLevel, nil
}

// User Management

func (s *AdminAgentReferralService) SearchUsersByInvitationCode(ctx context.Context, invitationCode string) ([]*model.User, error) {
	return s.adminRepo.SearchUsersByInvitationCode(ctx, invitationCode)
}

func (s *AdminAgentReferralService) GetUserReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error) {
	return s.invitationRepo.GetReferralSnapshot(ctx, userID)
}

func (s *AdminAgentReferralService) GetUsersByAgentLevel(ctx context.Context, input *GetUsersByLevelInput) (*GetUsersByLevelResponse, error) {
	// Set defaults
	if input.Page <= 0 {
		input.Page = 1
	}
	if input.PageSize <= 0 {
		input.PageSize = 10
	}
	if input.SortOrder == "" {
		input.SortOrder = "DESC"
	}

	offset := (input.Page - 1) * input.PageSize
	users, total, err := s.adminRepo.GetUsersByAgentLevel(ctx, input.LevelID, offset, input.PageSize, input.SortBy, input.SortOrder)
	if err != nil {
		return nil, fmt.Errorf("failed to get users by level: %w", err)
	}

	totalPages := int(math.Ceil(float64(total) / float64(input.PageSize)))

	return &GetUsersByLevelResponse{
		Users:      users,
		Total:      total,
		Page:       input.Page,
		PageSize:   input.PageSize,
		TotalPages: totalPages,
	}, nil
}

// Referral Tree Management

func (s *AdminAgentReferralService) GetAllReferralTrees(ctx context.Context, input *GetReferralTreesInput) (*GetReferralTreesResponse, error) {
	// Set defaults
	if input.Page <= 0 {
		input.Page = 1
	}
	if input.PageSize <= 0 {
		input.PageSize = 10
	}
	if input.SortOrder == "" {
		input.SortOrder = "DESC"
	}

	offset := (input.Page - 1) * input.PageSize
	trees, total, err := s.adminRepo.GetAllReferralTrees(ctx, offset, input.PageSize, input.SortBy, input.SortOrder)
	if err != nil {
		return nil, fmt.Errorf("failed to get referral trees: %w", err)
	}

	totalPages := int(math.Ceil(float64(total) / float64(input.PageSize)))

	return &GetReferralTreesResponse{
		Trees:      trees,
		Total:      total,
		Page:       input.Page,
		PageSize:   input.PageSize,
		TotalPages: totalPages,
	}, nil
}

func (s *AdminAgentReferralService) CreateReferralTreeSnapshot(ctx context.Context, input *CreateTreeSnapshotInput) (*model.InfiniteAgentReferralTree, error) {
	tree := &model.InfiniteAgentReferralTree{
		RootUserID:   input.RootUserID,
		SnapshotDate: time.Now(),
		// Additional fields will be calculated by background tasks
		// ID will be auto-generated by GORM
	}

	if err := s.adminRepo.CreateReferralTree(ctx, tree); err != nil {
		return nil, fmt.Errorf("failed to create referral tree snapshot: %w", err)
	}

	return tree, nil
}

func (s *AdminAgentReferralService) GetReferralTreeById(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentReferralTree, error) {
	return s.adminRepo.GetReferralTreeById(ctx, id)
}

func (s *AdminAgentReferralService) DeleteReferralTree(ctx context.Context, id uuid.UUID) error {
	return s.adminRepo.DeleteReferralTree(ctx, id)
}

// Analytics and Statistics

func (s *AdminAgentReferralService) GetAgentReferralStats(ctx context.Context, startDate, endDate time.Time) (*AgentReferralStatsResponse, error) {
	stats, err := s.adminRepo.GetAgentReferralStats(ctx, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get agent referral stats: %w", err)
	}

	return &AgentReferralStatsResponse{
		TotalUsers:          int(stats["total_users"].(int64)),
		TotalInfiniteAgents: int(stats["total_infinite_agents"].(int64)),
		TotalVolumeUSD:      stats["total_volume_usd"].(float64),
		TotalCommissionPaid: stats["total_commission_paid"].(float64),
		ActiveReferralTrees: int(stats["active_referral_trees"].(int64)),
		AverageTreeSize:     stats["average_tree_size"].(float64),
	}, nil
}

func (s *AdminAgentReferralService) GetCommissionDistributionStats(ctx context.Context, startDate, endDate time.Time) (*CommissionDistributionStatsResponse, error) {
	stats, err := s.adminRepo.GetCommissionDistributionStats(ctx, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get commission distribution stats: %w", err)
	}

	return &CommissionDistributionStatsResponse{
		DirectCommission:   stats["direct_commission"],
		IndirectCommission: stats["indirect_commission"],
		ExtendedCommission: stats["extended_commission"],
		InfiniteCommission: stats["infinite_commission"],
		TotalCommission:    stats["total_commission"],
	}, nil
}

func (s *AdminAgentReferralService) GetTopPerformingAgents(ctx context.Context, input *GetTopAgentsInput) ([]*TopAgentResponse, error) {
	users, err := s.adminRepo.GetTopPerformingAgents(ctx, input.Limit, input.SortBy)
	if err != nil {
		return nil, fmt.Errorf("failed to get top performing agents: %w", err)
	}

	var topAgents []*TopAgentResponse
	for _, user := range users {
		agent := &TopAgentResponse{
			UserID:         user.ID,
			InvitationCode: *user.InvitationCode,
			AgentLevel:     user.AgentLevel.Name,
		}

		if user.Email != nil {
			agent.Email = *user.Email
		}

		if user.ReferralSnapshot != nil {
			commissionEarned, _ := user.ReferralSnapshot.TotalCommissionEarnedUSD.Float64()
			perpsVolume, _ := user.ReferralSnapshot.TotalPerpsVolumeUSD.Float64()
			memeVolume, _ := user.ReferralSnapshot.TotalMemeVolumeUSD.Float64()

			agent.CommissionEarned = commissionEarned
			agent.VolumeGenerated = perpsVolume + memeVolume
			agent.ReferralCount = user.ReferralSnapshot.TotalDownlineCount
		}

		// Check if user is infinite agent
		// This would require a join or separate query in a real implementation
		agent.IsInfiniteAgent = false

		topAgents = append(topAgents, agent)
	}

	return topAgents, nil
}

// System Operations

func (s *AdminAgentReferralService) RecalculateAllReferralSnapshots(ctx context.Context) (*SystemOperationResult, error) {
	// This would trigger the snapshot recalculation task
	// For now, return a placeholder result
	return &SystemOperationResult{
		Success:        true,
		ProcessedCount: 0,
		ErrorCount:     0,
		Message:        "Referral snapshot recalculation triggered",
	}, nil
}

func (s *AdminAgentReferralService) RecalculateAllInfiniteAgentCommissions(ctx context.Context) (*SystemOperationResult, error) {
	// This would trigger the infinite agent commission recalculation
	// For now, return a placeholder result
	return &SystemOperationResult{
		Success:        true,
		ProcessedCount: 0,
		ErrorCount:     0,
		Message:        "Infinite agent commission recalculation triggered",
	}, nil
}

func (s *AdminAgentReferralService) SyncReferralTreeData(ctx context.Context) (*SystemOperationResult, error) {
	// This would trigger the referral tree data synchronization
	// For now, return a placeholder result
	return &SystemOperationResult{
		Success:        true,
		ProcessedCount: 0,
		ErrorCount:     0,
		Message:        "Referral tree data synchronization triggered",
	}, nil
}
