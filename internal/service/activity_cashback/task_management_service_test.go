package activity_cashback

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	// "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
)

// Mock repositories for testing
type MockActivityTaskRepository struct {
	mock.Mock
}

func (m *MockActivityTaskRepository) Create(ctx context.Context, task *model.ActivityTask) error {
	args := m.Called(ctx, task)
	return args.Error(0)
}

func (m *MockActivityTaskRepository) Update(ctx context.Context, task *model.ActivityTask) error {
	args := m.Called(ctx, task)
	return args.Error(0)
}

func (m *MockActivityTaskRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockActivityTaskRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.ActivityTask, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.ActivityTask), args.Error(1)
}

func (m *MockActivityTaskRepository) GetByCategoryID(ctx context.Context, categoryID uint) ([]model.ActivityTask, error) {
	args := m.Called(ctx, categoryID)
	return args.Get(0).([]model.ActivityTask), args.Error(1)
}

func (m *MockActivityTaskRepository) GetActive(ctx context.Context) ([]model.ActivityTask, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.ActivityTask), args.Error(1)
}

func (m *MockActivityTaskRepository) GetAvailable(ctx context.Context, now time.Time) ([]model.ActivityTask, error) {
	args := m.Called(ctx, now)
	return args.Get(0).([]model.ActivityTask), args.Error(1)
}

func (m *MockActivityTaskRepository) GetAll(ctx context.Context) ([]model.ActivityTask, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.ActivityTask), args.Error(1)
}

func (m *MockActivityTaskRepository) GetByFrequency(ctx context.Context, frequency model.TaskFrequency) ([]model.ActivityTask, error) {
	args := m.Called(ctx, frequency)
	return args.Get(0).([]model.ActivityTask), args.Error(1)
}

// Mock TierManagementService
type MockTierManagementService struct {
	mock.Mock
}

func (m *MockTierManagementService) AddPoints(ctx context.Context, userID uuid.UUID, points int, source string) error {
	args := m.Called(ctx, userID, points, source)
	return args.Error(0)
}

func (m *MockTierManagementService) CheckTierUpgrade(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.UserTierInfo), args.Error(1)
}

func (m *MockTierManagementService) GetUserTierInfo(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*model.UserTierInfo), args.Error(1)
}

func (m *MockTierManagementService) InitializeUserTierInfo(ctx context.Context, userID uuid.UUID) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockTierManagementService) UpdateTradingVolume(ctx context.Context, userID uuid.UUID, volumeUSD float64) error {
	args := m.Called(ctx, userID, volumeUSD)
	return args.Error(0)
}

func (m *MockTierManagementService) GetTierBenefits(ctx context.Context, tierLevel int) (*model.TierBenefit, error) {
	args := m.Called(ctx, tierLevel)
	return args.Get(0).(*model.TierBenefit), args.Error(1)
}

func (m *MockTierManagementService) GetAllTierBenefits(ctx context.Context) ([]model.TierBenefit, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.TierBenefit), args.Error(1)
}

func (m *MockTierManagementService) CalculateUserRank(ctx context.Context, userID uuid.UUID) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

// Mock TaskProgressService
type MockTaskProgressService struct {
	mock.Mock
}

func (m *MockTaskProgressService) GetTaskProgress(ctx context.Context, userID, taskID uuid.UUID) (*model.UserTaskProgress, error) {
	args := m.Called(ctx, userID, taskID)
	return args.Get(0).(*model.UserTaskProgress), args.Error(1)
}

func (m *MockTaskProgressService) InitializeTaskProgress(ctx context.Context, userID, taskID uuid.UUID) (*model.UserTaskProgress, error) {
	args := m.Called(ctx, userID, taskID)
	return args.Get(0).(*model.UserTaskProgress), args.Error(1)
}

func (m *MockTaskProgressService) UpdateProgress(ctx context.Context, userID, taskID uuid.UUID, increment int) error {
	args := m.Called(ctx, userID, taskID, increment)
	return args.Error(0)
}

func (m *MockTaskProgressService) SetProgress(ctx context.Context, userID, taskID uuid.UUID, value int) error {
	args := m.Called(ctx, userID, taskID, value)
	return args.Error(0)
}

func (m *MockTaskProgressService) CompleteProgress(ctx context.Context, userID, taskID uuid.UUID) error {
	args := m.Called(ctx, userID, taskID)
	return args.Error(0)
}

func (m *MockTaskProgressService) ResetProgress(ctx context.Context, userID, taskID uuid.UUID) error {
	args := m.Called(ctx, userID, taskID)
	return args.Error(0)
}

func (m *MockTaskProgressService) GetUserProgress(ctx context.Context, userID uuid.UUID) ([]model.UserTaskProgress, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]model.UserTaskProgress), args.Error(1)
}

func (m *MockTaskProgressService) GetCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error) {
	args := m.Called(ctx, userID, startDate, endDate)
	return args.Get(0).(map[string]int), args.Error(1)
}

func (m *MockTaskProgressService) GetUserStreaks(ctx context.Context, userID uuid.UUID) (map[string]int, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(map[string]int), args.Error(1)
}

func TestTaskManagementService_CompleteTask(t *testing.T) {
	// Setup mocks
	mockTaskRepo := &MockActivityTaskRepository{}
	mockTierService := &MockTierManagementService{}
	// mockProgressService := &MockTaskProgressService{}

	// Create service with mocks
	service := &TaskManagementService{
		// taskRepo:          mockTaskRepo,
		// completionFactory: activity_cashback.NewTaskCompletionRepositoryFactory(),
		// tierService:       mockTierService,
		// progressService:   mockProgressService,
	}

	ctx := context.Background()
	userID := uuid.New()
	taskID := uuid.New()
	verificationData := map[string]interface{}{"test": "data"}

	// Create test task
	task := &model.ActivityTask{
		ID:        taskID,
		Name:      "Test Task",
		Frequency: model.FrequencyDaily,
		Points:    100,
		IsActive:  true,
	}

	// Setup mock expectations
	mockTaskRepo.On("GetByID", ctx, taskID).Return(task, nil)
	mockTierService.On("AddPoints", ctx, userID, 100, mock.AnythingOfType("string")).Return(nil)
	mockTierService.On("CheckTierUpgrade", ctx, userID).Return(&model.UserTierInfo{}, nil)

	// Test successful completion
	t.Run("Successful Task Completion", func(t *testing.T) {
		err := service.CompleteTask(ctx, userID, taskID, verificationData)
		assert.NoError(t, err)

		// Verify mock calls
		mockTaskRepo.AssertExpectations(t)
		mockTierService.AssertExpectations(t)
	})

	// Test with inactive task
	t.Run("Inactive Task", func(t *testing.T) {
		inactiveTask := &model.ActivityTask{
			ID:        taskID,
			Name:      "Inactive Task",
			Frequency: model.FrequencyDaily,
			Points:    100,
			IsActive:  false,
		}

		mockTaskRepo.On("GetByID", ctx, taskID).Return(inactiveTask, nil).Once()

		err := service.CompleteTask(ctx, userID, taskID, verificationData)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "task is not active")
	})

	// Test with non-existent task
	t.Run("Non-existent Task", func(t *testing.T) {
		nonExistentTaskID := uuid.New()
		mockTaskRepo.On("GetByID", ctx, nonExistentTaskID).Return((*model.ActivityTask)(nil), assert.AnError).Once()

		err := service.CompleteTask(ctx, userID, nonExistentTaskID, verificationData)
		assert.Error(t, err)
	})
}

func TestTaskManagementService_GetTaskByID(t *testing.T) {
	mockTaskRepo := &MockActivityTaskRepository{}
	service := &TaskManagementService{
		// taskRepo: mockTaskRepo,
	}

	ctx := context.Background()
	taskID := uuid.New()

	task := &model.ActivityTask{
		ID:   taskID,
		Name: "Test Task",
	}

	mockTaskRepo.On("GetByID", ctx, taskID).Return(task, nil)

	result, err := service.GetTaskByID(ctx, taskID)
	assert.NoError(t, err)
	assert.Equal(t, task, result)

	mockTaskRepo.AssertExpectations(t)
}

func TestTaskManagementService_GetActiveTasks(t *testing.T) {
	mockTaskRepo := &MockActivityTaskRepository{}
	// service := &TaskManagementService{
	// 	taskRepo: mockTaskRepo,
	// }

	ctx := context.Background()

	tasks := []model.ActivityTask{
		{ID: uuid.New(), Name: "Task 1", IsActive: true},
		{ID: uuid.New(), Name: "Task 2", IsActive: true},
	}

	mockTaskRepo.On("GetActive", ctx).Return(tasks, nil)

	// result, err := service.GetActiveTasks(ctx)
	// assert.NoError(t, err)
	// assert.Equal(t, tasks, result)

	mockTaskRepo.AssertExpectations(t)
}

func TestTaskManagementService_VerifyTaskCompletion(t *testing.T) {
	mockTaskRepo := &MockActivityTaskRepository{}
	// service := &TaskManagementService{
	// 	taskRepo: mockTaskRepo,
	// }

	// ctx := context.Background()
	// userID := uuid.New()
	// taskID := uuid.New()
	// verificationData := map[string]interface{}{"test": "data"}

	// task := &model.ActivityTask{
	// 	ID:                 taskID,
	// 	Name:               "Test Task",
	// 	TaskType:           model.TaskTypeDaily,
	// 	VerificationMethod: &[]model.VerificationMethod{model.VerificationAuto}[0],
	// }

	// mockTaskRepo.On("GetByID", ctx, taskID).Return(task, nil)

	// result, err := service.VerifyTaskCompletion(ctx, userID, taskID, verificationData)
	// assert.NoError(t, err)
	// assert.True(t, result) // Auto verification should return true

	mockTaskRepo.AssertExpectations(t)
}

// TestHasUserMissedConsecutiveCheckin tests the consecutive check-in miss detection logic
func TestHasUserMissedConsecutiveCheckin(t *testing.T) {
	// Create a minimal service instance for testing the hasUserMissedConsecutiveCheckin method
	service := &TaskManagementService{}

	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	twoDaysAgo := now.AddDate(0, 0, -2)

	t.Run("No_Progress_No_Miss", func(t *testing.T) {
		// User with no streak and no completed milestones should not be considered as missing
		progress := model.UserTaskProgress{
			StreakCount:     0,
			CompletionCount: 0,
			LastCompletedAt: nil,
		}

		result := service.hasUserMissedConsecutiveCheckin(progress)
		assert.False(t, result, "User with no progress should not be considered as missing")
	})

	t.Run("Active_Streak_No_Miss_Yesterday", func(t *testing.T) {
		// User with active streak who checked in yesterday should not be considered as missing
		progress := model.UserTaskProgress{
			StreakCount:     2,
			CompletionCount: 0,
			LastCompletedAt: &yesterday,
		}

		result := service.hasUserMissedConsecutiveCheckin(progress)
		assert.False(t, result, "User who checked in yesterday should not be considered as missing")
	})

	t.Run("Active_Streak_No_Miss_Today", func(t *testing.T) {
		// User with active streak who checked in today should not be considered as missing
		progress := model.UserTaskProgress{
			StreakCount:     1,
			CompletionCount: 0,
			LastCompletedAt: &now,
		}

		result := service.hasUserMissedConsecutiveCheckin(progress)
		assert.False(t, result, "User who checked in today should not be considered as missing")
	})

	t.Run("Active_Streak_Missed_TwoDaysAgo", func(t *testing.T) {
		// User with active streak who last checked in two days ago should be considered as missing
		progress := model.UserTaskProgress{
			StreakCount:     3,
			CompletionCount: 0,
			LastCompletedAt: &twoDaysAgo,
		}

		result := service.hasUserMissedConsecutiveCheckin(progress)
		assert.True(t, result, "User who last checked in two days ago should be considered as missing")
	})

	t.Run("Completed_Milestone_Zero_Streak_No_Miss_Yesterday", func(t *testing.T) {
		// BUG FIX TEST: User who completed milestone 1 (CompletionCount=1, StreakCount=0)
		// and checked in yesterday should not be considered as missing
		progress := model.UserTaskProgress{
			StreakCount:     0, // Reset to 0 after milestone transition
			CompletionCount: 1, // Completed first milestone
			LastCompletedAt: &yesterday,
		}

		result := service.hasUserMissedConsecutiveCheckin(progress)
		assert.False(t, result, "User with completed milestone who checked in yesterday should not be missing")
	})

	t.Run("Completed_Milestone_Zero_Streak_Missed_TwoDaysAgo", func(t *testing.T) {
		// BUG FIX TEST: User who completed milestone 1 (CompletionCount=1, StreakCount=0)
		// and last checked in two days ago should be considered as missing and reset to first milestone
		progress := model.UserTaskProgress{
			StreakCount:     0, // Reset to 0 after milestone transition
			CompletionCount: 1, // Completed first milestone
			LastCompletedAt: &twoDaysAgo,
		}

		result := service.hasUserMissedConsecutiveCheckin(progress)
		assert.True(t, result, "User with completed milestone who missed two days should be considered as missing")
	})

	t.Run("Multiple_Milestones_Zero_Streak_Missed", func(t *testing.T) {
		// User who completed multiple milestones but has zero streak and missed check-in
		progress := model.UserTaskProgress{
			StreakCount:     0, // Reset to 0 after milestone transition
			CompletionCount: 2, // Completed two milestones
			LastCompletedAt: &twoDaysAgo,
		}

		result := service.hasUserMissedConsecutiveCheckin(progress)
		assert.True(t, result, "User with multiple completed milestones who missed should be reset")
	})

	t.Run("Never_Completed_No_Miss", func(t *testing.T) {
		// User who never completed any check-in should not be considered as missing
		progress := model.UserTaskProgress{
			StreakCount:     0,
			CompletionCount: 0,
			LastCompletedAt: nil, // Never completed
		}

		result := service.hasUserMissedConsecutiveCheckin(progress)
		assert.False(t, result, "User who never completed should not be considered as missing")
	})
}

// TestConsecutiveCheckinResetScenario tests the specific bug scenario described in the issue
func TestConsecutiveCheckinResetScenario(t *testing.T) {
	service := &TaskManagementService{}

	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	twoDaysAgo := now.AddDate(0, 0, -2)

	t.Run("Bug_Scenario_Milestone_1_Completed_Then_Missed", func(t *testing.T) {
		// SCENARIO: User completes milestone 1 (1/1 day), transitions to milestone 2 (0/2 days),
		// then misses a day. They should be reset to milestone 1 (0/1 days), not stay at milestone 2.

		// Step 1: User completes milestone 1 and transitions to milestone 2
		// After transition: StreakCount=0, CompletionCount=1, Status=NOT_STARTED, ProgressValue=0, TargetValue=2

		// Step 2: User misses a day (last check-in was two days ago, which is a clear miss)
		// This should be detected as a missed check-in
		progressMissedTwoDays := model.UserTaskProgress{
			StreakCount:     0,           // Reset to 0 after milestone transition (as per requirement)
			CompletionCount: 1,           // Completed first milestone
			LastCompletedAt: &twoDaysAgo, // Last check-in was two days ago - clear miss
		}

		result := service.hasUserMissedConsecutiveCheckin(progressMissedTwoDays)
		assert.True(t, result, "User who completed milestone 1 and missed two days should be detected as missing")

		// Step 3: User who checked in yesterday should NOT be considered missing (still consecutive)
		progressYesterday := model.UserTaskProgress{
			StreakCount:     0,          // Reset to 0 after milestone transition
			CompletionCount: 1,          // Completed first milestone
			LastCompletedAt: &yesterday, // Last check-in was yesterday - still consecutive
		}

		result2 := service.hasUserMissedConsecutiveCheckin(progressYesterday)
		assert.False(t, result2, "User who completed milestone 1 and checked in yesterday should NOT be missing")
	})

	t.Run("Bug_Scenario_Multiple_Milestones_Then_Missed", func(t *testing.T) {
		// SCENARIO: User completes multiple milestones, then misses a day
		// They should be reset to milestone 1, regardless of how many milestones they completed

		progressMultipleMilestones := model.UserTaskProgress{
			StreakCount:     0,           // Reset to 0 after milestone transition
			CompletionCount: 2,           // Completed two milestones
			LastCompletedAt: &twoDaysAgo, // Missed check-in
		}

		result := service.hasUserMissedConsecutiveCheckin(progressMultipleMilestones)
		assert.True(t, result, "User who completed multiple milestones and then missed should be detected as missing")
	})

	t.Run("No_False_Positives_Recent_Checkin", func(t *testing.T) {
		// SCENARIO: User completed milestone 1, transitioned to milestone 2, and checked in recently
		// They should NOT be considered as missing

		progressRecentCheckin := model.UserTaskProgress{
			StreakCount:     0,    // Reset to 0 after milestone transition
			CompletionCount: 1,    // Completed first milestone
			LastCompletedAt: &now, // Checked in today
		}

		result := service.hasUserMissedConsecutiveCheckin(progressRecentCheckin)
		assert.False(t, result, "User who completed milestone and checked in recently should not be missing")
	})
}
