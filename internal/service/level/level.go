package level

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/dto/response"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
)

type LevelService struct {
	levelRepo repo.LevelRepo
}

func NewLevelService() service.LevelI {
	return &LevelService{
		levelRepo: repo.NewLevelRepository(),
	}
}

// GetAgentLevels retrieves all agent levels
func (s *LevelService) GetAgentLevels(ctx context.Context) ([]model.AgentLevel, error) {
	return s.levelRepo.GetAgentLevels(ctx)
}

// GetAgentLevelByID retrieves a specific agent level by ID
func (s *LevelService) GetAgentLevelByID(ctx context.Context, id uint) (*model.AgentLevel, error) {
	return s.levelRepo.GetAgentLevelByID(ctx, id)
}

// CreateAgentLevel creates a new agent level with validation
func (s *LevelService) CreateAgentLevel(ctx context.Context, level *model.AgentLevel) (*model.AgentLevel, error) {
	// Validate required fields
	if level.Name == "" {
		return nil, fmt.Errorf("agent level name is required")
	}

	// Validate commission rates are within valid range (0-1)
	directRate, _ := level.DirectCommissionRate.Float64()
	indirectRate, _ := level.IndirectCommissionRate.Float64()
	extendedRate, _ := level.ExtendedCommissionRate.Float64()
	memeFeeRebate, _ := level.MemeFeeRebate.Float64()

	if directRate < 0 || directRate > 1 {
		return nil, fmt.Errorf("direct commission rate must be between 0 and 1")
	}
	if indirectRate < 0 || indirectRate > 1 {
		return nil, fmt.Errorf("indirect commission rate must be between 0 and 1")
	}
	if extendedRate < 0 || extendedRate > 1 {
		return nil, fmt.Errorf("extended commission rate must be between 0 and 1")
	}
	if memeFeeRebate < 0 || memeFeeRebate > 1 {
		return nil, fmt.Errorf("meme fee rebate must be between 0 and 1")
	}

	// Create the level
	err := s.levelRepo.CreateAgentLevel(ctx, level)
	if err != nil {
		return nil, fmt.Errorf("failed to create agent level: %w", err)
	}

	return level, nil
}

// UpdateAgentLevel updates all fields of an agent level
func (s *LevelService) UpdateAgentLevel(ctx context.Context, level *model.AgentLevel) (*model.AgentLevel, error) {
	// Check if level exists
	_, err := s.levelRepo.GetAgentLevelByID(ctx, level.ID)
	if err != nil {
		return nil, fmt.Errorf("agent level not found: %w", err)
	}

	// Validate required fields
	if level.Name == "" {
		return nil, fmt.Errorf("agent level name is required")
	}

	// Validate commission rates are within valid range (0-1)
	directRate, _ := level.DirectCommissionRate.Float64()
	indirectRate, _ := level.IndirectCommissionRate.Float64()
	extendedRate, _ := level.ExtendedCommissionRate.Float64()
	memeFeeRebate, _ := level.MemeFeeRebate.Float64()

	if directRate < 0 || directRate > 1 {
		return nil, fmt.Errorf("direct commission rate must be between 0 and 1")
	}
	if indirectRate < 0 || indirectRate > 1 {
		return nil, fmt.Errorf("indirect commission rate must be between 0 and 1")
	}
	if extendedRate < 0 || extendedRate > 1 {
		return nil, fmt.Errorf("extended commission rate must be between 0 and 1")
	}
	if memeFeeRebate < 0 || memeFeeRebate > 1 {
		return nil, fmt.Errorf("meme fee rebate must be between 0 and 1")
	}

	// Update the level
	err = s.levelRepo.UpdateAgentLevel(ctx, level)
	if err != nil {
		return nil, fmt.Errorf("failed to update agent level: %w", err)
	}

	return level, nil
}

// DeleteAgentLevel deletes an agent level with validation
func (s *LevelService) DeleteAgentLevel(ctx context.Context, id uint) error {
	// Check if level exists
	_, err := s.levelRepo.GetAgentLevelByID(ctx, id)
	if err != nil {
		return fmt.Errorf("agent level not found: %w", err)
	}

	// Check if any users are using this level
	userCount, err := s.levelRepo.CountUsersByAgentLevel(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to check users using this level: %w", err)
	}

	if userCount > 0 {
		return fmt.Errorf("cannot delete agent level: %d users are currently using this level", userCount)
	}

	// Prevent deletion of default level (ID = 1)
	if id == 1 {
		return fmt.Errorf("cannot delete default agent level (Lv1)")
	}

	// Delete the level
	err = s.levelRepo.DeleteAgentLevel(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to delete agent level: %w", err)
	}

	return nil
}

// GetUserLevelInfo retrieves user's current level information including volume and next level requirements
func (s *LevelService) GetUserLevelInfo(ctx context.Context, userID uuid.UUID) (*response.UserLevelInfoResponse, error) {
	// First check if user exists
	var user model.User
	err := global.GVA_DB.WithContext(ctx).Where("id = ?", userID).First(&user).Error
	if err != nil {
		// User doesn't exist, return default level 1 with zero volumes
		defaultLevel, err := s.levelRepo.GetAgentLevelByID(ctx, 1)
		if err != nil {
			return &response.UserLevelInfoResponse{
				Success: false,
				Message: fmt.Sprintf("Failed to get default level: %v", err),
			}, nil
		}

		userLevelInfo := &response.UserLevelInfo{
			CurrentLevel:   defaultLevel,
			MemeVolume:     decimal.Zero,
			ContractVolume: decimal.Zero,
			TotalVolume:    decimal.Zero,
		}

		return &response.UserLevelInfoResponse{
			Success: true,
			Message: "User not found, returning default level 1 with zero volumes",
			Data:    userLevelInfo,
		}, nil
	}

	// User exists, get user with current level and proceed with existing logic
	err = global.GVA_DB.WithContext(ctx).Preload("AgentLevel").Where("id = ?", userID).First(&user).Error
	if err != nil {
		return &response.UserLevelInfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to get user with level: %v", err),
		}, nil
	}

	memeFeeRebate, err := s.calculateMemeFeeRebate(ctx, userID)
	if err != nil {
		return &response.UserLevelInfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to calculate meme fee rebate: %v", err),
		}, nil
	}

	contractFeeRebate, err := s.calculateContractFeeRebate(ctx, userID)
	if err != nil {
		return &response.UserLevelInfoResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to calculate contract fee rebate: %v", err),
		}, nil
	}

	totalCommissionEarnedFloat := memeFeeRebate.Add(contractFeeRebate)

	userLevelInfo := &response.UserLevelInfo{
		CurrentLevel:   &user.AgentLevel,
		MemeVolume:     memeFeeRebate,
		ContractVolume: contractFeeRebate,
		TotalVolume:    totalCommissionEarnedFloat,
	}

	return &response.UserLevelInfoResponse{
		Success: true,
		Message: "Successfully retrieved user level information",
		Data:    userLevelInfo,
	}, nil
}

// UpdateLevelCommission updates commission rates and meme fee rebate for a specific level
func (s *LevelService) UpdateLevelCommission(ctx context.Context, levelID uint, directRate, indirectRate, extendedRate, memeFeeRebate float64) (*model.AgentLevel, error) {
	// Validate input parameters
	if directRate < 0 || directRate > 1 {
		return nil, fmt.Errorf("direct commission rate must be between 0 and 1")
	}
	if indirectRate < 0 || indirectRate > 1 {
		return nil, fmt.Errorf("indirect commission rate must be between 0 and 1")
	}
	if extendedRate < 0 || extendedRate > 1 {
		return nil, fmt.Errorf("extended commission rate must be between 0 and 1")
	}
	if memeFeeRebate < 0 || memeFeeRebate > 1 {
		return nil, fmt.Errorf("meme fee rebate must be between 0 and 1")
	}

	// Get the current level
	level, err := s.levelRepo.GetAgentLevelByID(ctx, levelID)
	if err != nil {
		return nil, fmt.Errorf("failed to get agent level: %w", err)
	}

	// Update the commission rates and meme fee rebate
	level.DirectCommissionRate = decimal.NewFromFloat(directRate)
	level.IndirectCommissionRate = decimal.NewFromFloat(indirectRate)
	level.ExtendedCommissionRate = decimal.NewFromFloat(extendedRate)
	level.MemeFeeRebate = decimal.NewFromFloat(memeFeeRebate)

	// Save the updated level
	err = s.levelRepo.UpdateAgentLevel(ctx, level)
	if err != nil {
		return nil, fmt.Errorf("failed to update agent level: %w", err)
	}

	return level, nil
}

// calculateMemeFeeRebate calculates the total meme fee rebate earned by a user
func (s *LevelService) calculateMemeFeeRebate(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	query := global.GVA_DB.WithContext(ctx).
		Model(&model.MemeCommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ?", userID).
		Where("status = ?", "PENDING_CLAIM")

	err := query.Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate meme fee rebate: %w", err)
	}

	return result.TotalAmount, nil
}

// calculateContractFeeRebate calculates the total contract fee rebate earned by a user
func (s *LevelService) calculateContractFeeRebate(ctx context.Context, userID uuid.UUID) (decimal.Decimal, error) {
	var result struct {
		TotalAmount decimal.Decimal `json:"total_amount"`
	}

	err := global.GVA_DB.WithContext(ctx).
		Model(&model.CommissionLedger{}).
		Select("COALESCE(SUM(commission_amount), 0) as total_amount").
		Where("recipient_user_id = ? and status = ?",
			userID, "PENDING_CLAIM").
		Scan(&result).Error

	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate contract fee rebate: %w", err)
	}

	return result.TotalAmount, nil
}
