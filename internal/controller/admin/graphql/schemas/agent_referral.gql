# Admin Agent Referral Management Schema

# This file is intentionally minimal as all types are defined in the main schema.graphqls file
# to avoid duplication and conflicts during GraphQL generation.
type InfiniteAgentConfig {
  id: ID!
  userID: ID!
  commissionRateN: Float!
  # --- DETAILED CALCULATION COMPONENTS ---
  totalNetFeeUSD: String!
  totalStandardCommissionPaidUSD: String!
  # --- FINAL RESULT ---
  finalCommissionAmountUSD: String!
  # --- DETAILED FEE BREAKDOWN ---
  # Meme trading fee breakdown
  memeTotalFeeUSD: String!
  memePaidCommissionUSD: String!
  memeNetFeeUSD: String!
  memeActivityCashbackUSD: String!
  # Contract trading fee breakdown
  contractTotalFeeUSD: String!
  contractPaidCommissionUSD: String!
  contractNetFeeUSD: String!
  # --- STATUS AND METADATA ---
  status: String!
  createdAt: Time!
  updatedAt: Time!
  # Relationship
  user: User
}
enum StatusType {
  ACTIVE
  INACTIVE
}

input CreateInfiniteAgentConfigInput {
  userID: ID!
  commissionRateN: Float!
  status: StatusType!
}
type CreateInfiniteAgentConfigResponse {
  infiniteAgentConfig: InfiniteAgentConfig
  success: Boolean!
  message: String!
}

# User List Query Types
input GetUserListInput {
  userId: String
  walletAddress: String
  email: String
  invitationCode: String
  page: Int = 1
  pageSize: Int = 10
  sortBy: String
  sortOrder: String = "DESC"
}

type UserListResponse {
  users: [UserListInfo!]!
  total: Int!
  page: Int!
  pageSize: Int!
  totalPages: Int!
}

type UserListInfo {
  userId: ID!
  userLevel: String!
  walletAddresses: [String!]!
  user: User!
  userWallets: [UserWallet!]!
  referralSnapshot: ReferralSnapshotFull
  isInfiniteAgent: Boolean!
}

# User Info Query Types
input GetUserInfoInput {
  userId: ID!
}

type UserInfoResponse {
  userId: ID!
  isInfiniteAgent: Boolean!
  infiniteAgentConfig: InfiniteAgentConfig
  # UserListResponse fields
  users: [UserListInfo!]!
}

# User Wallet Type
type UserWallet {
  id: ID!
  userId: ID!
  walletAddress: String!
  walletType: String!
  isActive: Boolean!
  createdAt: Time!
  updatedAt: Time!
}
