# Admin Activity Cashback Schema - Types and Inputs

# Multilingual Name Type
type MultilingualName {
  en: String!
  zh: String
  ja: String
  hi: String
  hk: String
  vi: String
}

input MultilingualNameInput {
  en: String!
  zh: String
  ja: String
  hi: String
  hk: String
  vi: String
}

# Task Types
type ActivityTask {
  id: ID!
  categoryId: ID!
  category: TaskCategory!
  name: MultilingualName!
  description: String

  frequency: TaskFrequency!
  taskIdentifier: TaskIdentifier
  points: Int!
  maxCompletions: Int
  resetPeriod: String
  conditions: String
  actionTarget: String
  verificationMethod: String
  externalLink: String
  taskIcon: String # New field for task icon
  buttonText: String # New field for button text
  startDate: Int64
  endDate: Int64
  sortOrder: Int!
  isActive: Boolean!
  createdAt: Time!
  updatedAt: Time!
}
type ALlTaskCategoriesResponse {
  data: [TaskCategory!]!
  total: Int!
  page: Int!
  pageSize: Int!
  totalPages: Int!
}

type TaskCategory {
  id: ID!
  name: TaskCategoryName!
  displayName: String!
  description: String
  icon: String
  isActive: Boolean!
  sortOrder: Int!
  createdAt: Time!
  updatedAt: Time!
}

# Enums
enum TaskCategoryName {
  DAILY
  COMMUNITY
  TRADING
}



enum TaskFrequency {
  DAILY
  PROGRESSIVE
  ONE_TIME
  MANUAL
  UNLIMITED
}

enum TaskIdentifier {
  # Daily Tasks
  DAILY_CHECKIN
  MEME_TRADE_DAILY
  PERPETUAL_TRADE_DAILY
  MARKET_PAGE_VIEW
  CHECK_MARKET_TRENDS

  # Community Tasks
  TWITTER_FOLLOW
  TWITTER_RETWEET
  TWITTER_LIKE
  TELEGRAM_JOIN
  INVITE_FRIENDS
  SHARE_REFERRAL
  SHARE_EARNINGS_CHART

  # Trading Tasks
  TRADING_POINTS
}

# User Tier Types
type UserTierInfo {
  userId: ID!
  email: String
  currentTier: TierBenefit
  totalPoints: Int!
  availableCashback: Float!
  totalCashbackClaimed: Float!
  nextTier: TierBenefit
  pointsToNextTier: Int
  lastActivityAt: Time

  pointsThisMonth: Int!
  tradingVolumeUsd: Float!
  activeDaysThisMonth: Int!
  cumulativeCashbackUsd: Float!
  claimableCashbackUsd: Float!
  claimedCashbackUsd: Float!
  lastActivityDate: Time
  tierUpgradedAt: Time
  monthlyResetAt: Time
  createdAt: Time!
  updatedAt: Time!
  userRank: Int

}

input UserTierInfoInput {
  userId: ID!
}
type ALlTierBenefitsResponse {
  data: [TierBenefit!]!
  total: Int!
  page: Int!
  pageSize: Int!
  totalPages: Int!
}
type TierBenefit {
  id: ID!
  tierLevel: Int!
  tierName: String!
  minPoints: Int!
  cashbackPercentage: Float!
  referredIncentivePercentage: Float!
  netFee: Float!
  benefitsDescription: String
  tierColor: String
  tierIcon: String
  isActive: Boolean!
  createdAt: Time!
  updatedAt: Time!
}

type TierBenefitResponse {
  success: Boolean!
  message: String!
  data: TierBenefit
}

# Input Types
input CreateTaskInput {
  categoryId: ID!
  name: MultilingualNameInput!
  description: String

  frequency: TaskFrequency!
  taskIdentifier: TaskIdentifier # Optional unique identifier for task processing
  points: Int!
  maxCompletions: Int
  resetPeriod: String
  conditions: String # JSON string
  actionTarget: String
  verificationMethod: String
  externalLink: String
  taskIcon: String # New field for task icon
  buttonText: String # New field for button text
  startDate: Int64
  endDate: Int64
  sortOrder: Int
}

input UpdateTaskInput {
  id: ID!
  categoryId: ID
  name: MultilingualNameInput
  description: String

  frequency: TaskFrequency
  taskIdentifier: TaskIdentifier
  points: Int
  maxCompletions: Int
  resetPeriod: String
  conditions: String # JSON string
  actionTarget: String
  verificationMethod: String
  externalLink: String
  taskIcon: String # New field for task icon
  buttonText: String # New field for button text
  startDate: Int64
  endDate: Int64
  sortOrder: Int
  isActive: Boolean
}
input TaskCenterInput{
  userId: String!
}
# Input for consecutive check-in milestone configuration
input ConsecutiveCheckinMilestoneInput {
  days: Int!     # Number of consecutive days required
  points: Int!   # Points awarded when milestone is reached
  name: MultilingualNameInput # Multilingual name for the milestone
  taskIcon: String # Icon for the milestone
}

# Input for creating consecutive check-in task with configurable milestones
input CreateConsecutiveCheckinTaskInput {
  categoryId: ID!
  name: MultilingualNameInput!
  description: String
  milestones: [ConsecutiveCheckinMilestoneInput!]! # Array of milestones (e.g., [3,7,30] days)
  actionTarget: String
  verificationMethod: String
  taskIcon: String
  buttonText: String
  startDate: Int64
  endDate: Int64
  sortOrder: Int
}

input CreateAccumulatedMEMETradingVolumeTaskInput {
  volumeThreshold: Float!
  points: Int!
  categoryId: ID
  name: MultilingualNameInput
  description: String
  maxCompletions: Int
  actionTarget: String
  verificationMethod: String
  externalLink: String
  taskIcon: String
  buttonText: String
  startDate: Int64
  endDate: Int64
  sortOrder: Int
}

input CreateTaskCategoryInput {
  name: TaskCategoryName!
  displayName: String!
  description: String
  icon: String
  sortOrder: Int
}

input UpdateTaskCategoryInput {
  id: ID!
  name: TaskCategoryName
  displayName: String
  description: String
  icon: String
  isActive: Boolean
  sortOrder: Int
}
input AllTaskCategoriesInput {
  page: Int = 1
  pageSize: Int = 10
  sortBy: String
  sortOrder: String = "DESC"
}
input CreateTierBenefitInput {
  tierLevel: Int!
  tierName: String!
  minPoints: Int!
  cashbackPercentage: Float!
  referredIncentivePercentage: Float = 0.05
  netFee: Float!
  benefitsDescription: String
  tierColor: String
  tierIcon: String
}

input UpdateTierBenefitInput {
  id: ID!
  tierLevel: Int
  tierName: String
  minPoints: Int
  cashbackPercentage: Float
  referredIncentivePercentage: Float
  netFee: Float
  benefitsDescription: String
  tierColor: String
  tierIcon: String
  isActive: Boolean
}
input AllTierBenefitsInput {
  page: Int = 1
  pageSize: Int = 10
  sortBy: String
  sortOrder: String = "DESC"
}
# Admin input types
input AdminStatsInput {
  startDate: Time!
  endDate: Time!
}

# Admin response types
type AdminTaskCompletionStatsResponse {
  success: Boolean!
  message: String!
  data: AdminTaskCompletionStats
}

type AdminTaskCompletionStats {
  taskCompletions: [TaskCompletionStat!]!
  startDate: Time!
  endDate: Time!
  totalTasks: Int!
}

type TaskCompletionStat {
  taskName: String!
  completionCount: Int!
}

type AdminUserActivityStatsResponse {
  success: Boolean!
  message: String!
  data: AdminUserActivityStats
}

type AdminUserActivityStats {
  dailyCompletions: [DailyCompletionStat!]!
  startDate: Time!
  endDate: Time!
}

type DailyCompletionStat {
  date: String!
  completionCount: Int!
}

type AdminTierDistributionResponse {
  success: Boolean!
  message: String!
  data: [TierDistributionStat!]!
}

type TierDistributionStat {
  tierLevel: Int!
  userCount: Int!
}





enum TaskStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  CLAIMED
  EXPIRED
}

enum ClaimType {
  TRADING_CASHBACK
  TASK_REWARD
  TIER_BONUS
  REFERRAL_BONUS
}

enum ClaimStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}




type UserTaskProgress {
  id: ID!
  userId: ID!
  taskId: ID!
  status: TaskStatus!
  progressValue: Int!
  targetValue: Int
  completionCount: Int!
  pointsEarned: Int!
  lastCompletedAt: Time
  lastResetAt: Time
  streakCount: Int!
  metadata: String # JSON string
  createdAt: Time!
  updatedAt: Time!
  progressPercentage: Float!
  canBeClaimed: Boolean!
}



type ActivityCashbackClaim {
  id: ID!
  userId: ID!
  claimType: ClaimType!
  totalAmountUsd: Float!
  totalAmountSol: Float!
  transactionHash: String
  status: ClaimStatus!
  claimedAt: Time!
  processedAt: Time
  metadata: String # JSON string
  createdAt: Time!
  updatedAt: Time!
}

type TaskCompletionHistory {
  id: ID!
  userId: ID!
  taskId: ID!
  pointsAwarded: Int!
  completionDate: Time!
  verificationData: String # JSON string
  createdAt: Time!
  task: ActivityTask
}

type UserDashboard {
  userTierInfo: UserTierInfo!
  tierBenefit: TierBenefit!
  nextTier: TierBenefit
  pointsToNextTier: Int!
  claimableCashback: Float!
  recentClaims: [ActivityCashbackClaim!]!
  userRank: Int!
}

type TaskCenter {
  categories: [TaskCategoryWithTasks!]!
  userProgress: [UserTaskProgress!]!
  completedToday: Int!
  pointsEarnedToday: Int!
  streakTasks: [UserTaskProgress!]!
}

type TaskCategoryWithTasks {
  category: TaskCategory!
  tasks: [TaskWithProgress!]!
}

type TaskWithProgress {
  task: ActivityTask!
  progress: UserTaskProgress
}



# Activity Cashback Summary for UI
type ActivityCashbackSummary {
  # Current ranking info
  currentLevel: Int!
  currentLevelName: String!
  nextLevel: Int
  nextLevelName: String

  # Progress calculation
  currentScore: Int!
  totalScoreForNextLevel: Int
  scoreRequiredToUpgrade: Int
  progressPercentage: Float!

  # Trading volume (MEME only for now)
  accumulatedTradingVolumeUsd: Float!

  # Activity tracking
  activeLogonDays: Int!

  # Cashback information
  accumulatedCashbackUsd: Float!
  claimableCashbackUsd: Float!
  claimedCashbackUsd: Float!

  # Additional tier info
  currentTierColor: String
  currentTierIcon: String
  nextTierColor: String
  nextTierIcon: String
}

type ActivityCashbackSummaryResponse {
  success: Boolean!
  message: String!
  data: ActivityCashbackSummary
}

# Response Types
type UserDashboardResponse {
  success: Boolean!
  message: String!
  data: UserDashboard
}

type TaskCenterResponse {
  success: Boolean!
  message: String!
  data: TaskCenter
}



type TaskCompletionResponse {
  success: Boolean!
  message: String!
  pointsAwarded: Int!
  newTierLevel: Int
  tierUpgraded: Boolean!
  isPending: Boolean!
  remainingWaitTimeSeconds: Int
  completionTime: Time
}


type CashbackClaimResponse {
  success: Boolean!
  message: String!
  claimId: ID!
  amountUsd: Float!
  amountSol: Float!
}

type TierBenefitsResponse {
  success: Boolean!
  message: String!
  data: [TierBenefit!]!
}



type UserTaskProgressResponse {
  success: Boolean!
  message: String!
  data: [UserTaskProgress!]!
}

type UserTaskListByCategoryResponse {
  success: Boolean!
  message: String!
  data: [TaskWithProgress!]!
}

type TaskCompletionHistoryResponse {
  success: Boolean!
  message: String!
  data: [TaskCompletionHistory!]!
  total: Int!
}

# Input Types
input CompleteTaskInput {
    userId: ID!
  taskId: ID!
  verificationData: String # JSON string
}
input ActivityCashbackSummaryInput{
    userId: ID!
}

input TasksByIdsInput{
    taskIds: [ID!]!
}
input ActivityCashbackDashboardInput {
  userId: ID!
}
input ClaimCashbackInput {
  userId: ID!
  amountUsd: Float!
}

input UserTaskListByCategoryInput {
  categoryName: TaskCategoryName!
}

input TaskCompletionHistoryInput {
    userId: ID!
  taskId: ID
  startDate: Time
  endDate: Time
  limit: Int
  offset: Int
}

