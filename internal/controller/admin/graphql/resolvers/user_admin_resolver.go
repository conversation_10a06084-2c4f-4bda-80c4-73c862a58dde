package resolvers

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/user_admin"
)

// AdminUserResolver handles admin user GraphQL operations
type AdminUserResolver struct {
	service user_admin.AdminUserServiceInterface
}

// NewAdminUserResolver creates a new resolver instance
func NewAdminUserResolver() *AdminUserResolver {
	return &AdminUserResolver{
		service: user_admin.NewAdminUserService(),
	}
}

// AdminGetUserList retrieves a paginated list of users with optional filtering
func (r *AdminUserResolver) AdminGetUserList(ctx context.Context, input gql_model.GetUserListInput) (*gql_model.UserListResponse, error) {
	return r.service.GetUserList(ctx, input)
}

// AdminGetUserInfo retrieves detailed information about a specific user
func (r *AdminUserResolver) AdminGetUserInfo(ctx context.Context, input gql_model.GetUserInfoInput) (*gql_model.UserInfoResponse, error) {
	return r.service.GetUserInfo(ctx, input)
}

// AdminManualLevelUpgrade manually upgrades a user's level (overrides automatic upgrade)
func (r *AdminUserResolver) AdminManualLevelUpgrade(ctx context.Context, input gql_model.ManualLevelUpgradeInput) (*gql_model.ManualLevelUpgradeResponse, error) {
	return r.service.ManualLevelUpgrade(ctx, input)
}
