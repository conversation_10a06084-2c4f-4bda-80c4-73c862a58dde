package middlewares

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"strings"

	"github.com/gin-gonic/gin"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
)

// isIntrospectionQuery checks if the request is a GraphQL introspection query
func isIntrospectionQuery(body []byte) bool {
	var request struct {
		Query string `json:"query"`
	}

	if err := json.Unmarshal(body, &request); err != nil {
		return false
	}

	// Check for common introspection query patterns
	query := strings.TrimSpace(request.Query)
	return strings.Contains(query, "__schema") ||
		strings.Contains(query, "__type") ||
		strings.Contains(query, "IntrospectionQuery")
}

// ApiKeyAuth middleware for validating API key authentication
// Allows introspection queries to bypass authentication for schema discovery
func ApiKeyAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// Read the request body to check if it's an introspection query
		body, err := io.ReadAll(ctx.Request.Body)
		if err != nil {
			ctx.JSON(400, gin.H{
				"error":   "bad request",
				"message": "failed to read request body",
			})
			ctx.Abort()
			return
		}

		// Restore the request body for downstream handlers
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))

		// Allow introspection queries without authentication
		if isIntrospectionQuery(body) {
			// Set a special context to indicate this is an introspection query
			wrappedCtx := context.WithValue(ctx.Request.Context(), "isIntrospection", true)
			ctx.Request = ctx.Request.WithContext(wrappedCtx)
			ctx.Next()
			return
		}

		// For non-introspection queries, require API key authentication
		apiKey := ctx.GetHeader("x-api-key")
		if apiKey == "" {
			// Try alternative header names
			apiKey = ctx.GetHeader("X-API-Key")
			if apiKey == "" {
				apiKey = ctx.GetHeader("Authorization")
				if strings.HasPrefix(apiKey, "Bearer ") {
					apiKey = strings.TrimPrefix(apiKey, "Bearer ")
				}
			}
		}
		// Validate API key
		//Temporarily remove ApiKeyAuth middleware for easy testing todo  Don't merge into prod staging branches
		if apiKey == "" || apiKey != global.GVA_CONFIG.Admin.InternalAPIKey {
			ctx.JSON(401, gin.H{
				"error":   "unauthorized",
				"message": "invalid or missing API key",
			})
			ctx.Abort()
			return
		}

		// Set admin context for GraphQL resolvers
		wrappedCtx := context.WithValue(ctx.Request.Context(), "isAdmin", true)
		wrappedCtx = context.WithValue(wrappedCtx, "adminApiKey", apiKey)
		ctx.Request = ctx.Request.WithContext(wrappedCtx)
		ctx.Next()
	}
}

// ValidateApiKey validates API key without middleware context
func ValidateApiKey(apiKey string) bool {
	return apiKey != "" && apiKey == global.GVA_CONFIG.Admin.InternalAPIKey
}
