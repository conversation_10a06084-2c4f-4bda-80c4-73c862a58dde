package agent_referral

import (
	"fmt"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gorm.io/gorm"
)

type SnapshotTask struct {
	repo repo.InvitationRepo
}

func NewSnapshotTask(repo repo.InvitationRepo) *SnapshotTask {
	return &SnapshotTask{
		repo: repo,
	}
}

// UpdateAllReferralSnapshots updates agent referral snapshot statistics for all users
// This includes direct count, total downline count, total volume USD, total rewards distributed, and upline relationships
func (s *SnapshotTask) UpdateAllReferralSnapshots() {
	var userIDs []uuid.UUID
	global.GVA_DB.Model(&model.User{}).Pluck("id", &userIDs)

	processedCount := 0
	errorCount := 0

	for _, userID := range userIDs {
		if err := s.updateUserReferralSnapshot(userID); err != nil {
			// global.GVA_LOG.Error("Failed to update referral snapshot for user",
			// 	zap.String("user_id", userID.String()),
			// 	zap.Error(err))
			errorCount++
		} else {
			processedCount++
		}
	}

	// global.GVA_LOG.Info("Referral snapshot update completed",
	// 	zap.Int("total_users", len(userIDs)),
	// 	zap.Int("processed_count", processedCount),
	// 	zap.Int("error_count", errorCount))
}

// updateUserReferralSnapshot updates referral snapshot for a single user
func (s *SnapshotTask) updateUserReferralSnapshot(userID uuid.UUID) error {
	// global.GVA_LOG.Debug("Processing referral snapshot for user", zap.String("user_id", userID.String()))

	// Get upline relationships (L1, L2, L3) first to ensure they are always processed
	var l1UplineID, l2UplineID, l3UplineID *uuid.UUID
	var referral model.Referral
	err := global.GVA_DB.Where("user_id = ?", userID).First(&referral).Error
	if err == nil && referral.ReferrerID != nil {
		// L1 upline is the direct referrer
		l1UplineID = referral.ReferrerID

		// Find L2 upline (referrer of L1)
		var l1Referral model.Referral
		err = global.GVA_DB.Where("user_id = ?", *l1UplineID).First(&l1Referral).Error
		if err == nil && l1Referral.ReferrerID != nil {
			l2UplineID = l1Referral.ReferrerID

			// Find L3 upline (referrer of L2)
			var l2Referral model.Referral
			err = global.GVA_DB.Where("user_id = ?", *l2UplineID).First(&l2Referral).Error
			if err == nil && l2Referral.ReferrerID != nil {
				l3UplineID = l2Referral.ReferrerID
			}
		}
	}

	// Calculate direct referral count (depth = 1)
	var directCount int64
	global.GVA_DB.Model(&model.Referral{}).
		Where("referrer_id = ? AND depth = 1", userID).
		Count(&directCount)

	// Calculate total downline count (depth <= 3)
	var totalDownlineCount int64
	global.GVA_DB.Model(&model.Referral{}).
		Where("referrer_id = ? AND depth <= 3", userID).
		Count(&totalDownlineCount)

	// Get trading users count
	tradingUserCount, err := s.GetTradingUsersCount(userID)
	if err != nil {
		tradingUserCount = 0
		// global.GVA_LOG.Warn("Failed to get trading users count, setting to 0",
		// 	zap.String("user_id", userID.String()), zap.Error(err))
	}

	// Get personal perps volume USD
	totalPerpsVolumeUSD, err := s.GetPersonalPerpsVolumeUSD(userID)
	if err != nil {
		totalPerpsVolumeUSD = decimal.Zero
		// global.GVA_LOG.Warn("Failed to get personal perps volume, setting to 0",
		// 	zap.String("user_id", userID.String()), zap.Error(err))
	}

	// Get personal meme volume USD
	totalMemeVolumeUSD, err := s.GetPersonalMemeVolumeUSD(userID)
	if err != nil {
		totalMemeVolumeUSD = decimal.Zero
		// global.GVA_LOG.Warn("Failed to get personal meme volume, setting to 0",
		// 	zap.String("user_id", userID.String()), zap.Error(err))
	}

	// Get commission amounts
	claimedCommissionUSD, err := s.GetClaimedCommissionUSD(userID)
	if err != nil {
		claimedCommissionUSD = decimal.Zero
		// global.GVA_LOG.Warn("Failed to get claimed commission, setting to 0",
		// 	zap.String("user_id", userID.String()), zap.Error(err))
	}

	unclaimedCommissionUSD, err := s.GetUnclaimedCommissionUSD(userID)
	if err != nil {
		unclaimedCommissionUSD = decimal.Zero
		// global.GVA_LOG.Warn("Failed to get unclaimed commission, setting to 0",
		// 	zap.String("user_id", userID.String()), zap.Error(err))
	}

	// Get cashback amounts
	claimedCashbackUSD, err := s.GetClaimedCashbackUSD(userID)
	if err != nil {
		claimedCashbackUSD = decimal.Zero
		// global.GVA_LOG.Warn("Failed to get claimed cashback, setting to 0",
		// 	zap.String("user_id", userID.String()), zap.Error(err))
	}

	unclaimedCashbackUSD, err := s.GetUnclaimedCashbackUSD(userID)
	if err != nil {
		unclaimedCashbackUSD = decimal.Zero
		// global.GVA_LOG.Warn("Failed to get unclaimed cashback, setting to 0",
		// 	zap.String("user_id", userID.String()), zap.Error(err))
	}

	// Get total perps fees
	totalPerpsFees, err := s.GetTotalPerpsFees(userID)
	if err != nil {
		totalPerpsFees = decimal.Zero
		// global.GVA_LOG.Warn("Failed to get total perps fees, setting to 0",
		// 	zap.String("user_id", userID.String()), zap.Error(err))
	}

	// Get total meme fees
	totalMemeFees, err := s.GetTotalMemeFees(userID)
	if err != nil {
		totalMemeFees = decimal.Zero
		// global.GVA_LOG.Warn("Failed to get total meme fees, setting to 0",
		// 	zap.String("user_id", userID.String()), zap.Error(err))
	}

	// Calculate total amounts
	totalCommissionEarnedUSD := claimedCommissionUSD.Add(unclaimedCommissionUSD)
	totalCashbackEarnedUSD := claimedCashbackUSD.Add(unclaimedCashbackUSD)

	// Calculate paid and unpaid fees based on new definition:
	// Perps fees: paid = TotalCommissionEarnedUSD, unpaid = TotalPerpsFees - TotalCommissionEarnedUSD
	// Meme fees: paid = TotalCashbackEarnedUSD, unpaid = TotalMemeFees - TotalCashbackEarnedUSD
	// Ensure no negative values for unpaid fees
	totalPerpsFeesPaid := totalCommissionEarnedUSD
	totalPerpsFeesUnPaid := totalPerpsFees.Sub(totalCommissionEarnedUSD)
	if totalPerpsFeesUnPaid.IsNegative() {
		totalPerpsFeesUnPaid = decimal.Zero
	}

	totalMemeFeesPaid := totalCashbackEarnedUSD
	totalMemeFeesUnPaid := totalMemeFees.Sub(totalCashbackEarnedUSD)
	if totalMemeFeesUnPaid.IsNegative() {
		totalMemeFeesUnPaid = decimal.Zero
	}

	// Get trade counts
	totalPerpsTradesCount, err := s.GetPersonalPerpsTradesCount(userID)
	if err != nil {
		totalPerpsTradesCount = 0
		// global.GVA_LOG.Warn("Failed to get personal perps trades count, setting to 0",
		// 	zap.String("user_id", userID.String()), zap.Error(err))
	}

	totalMemeTradesCount, err := s.GetPersonalMemeTradesCount(userID)
	if err != nil {
		totalMemeTradesCount = 0
		// global.GVA_LOG.Warn("Failed to get personal meme trades count, setting to 0",
		// 	zap.String("user_id", userID.String()), zap.Error(err))
	}

	// Create or update ReferralSnapshot
	var snapshot model.ReferralSnapshot
	err = global.GVA_DB.Where("user_id = ?", userID).First(&snapshot).Error
	if err == gorm.ErrRecordNotFound {
		// Create new snapshot
		snapshot = model.ReferralSnapshot{
			UserID:                   userID,
			DirectCount:              int(directCount),
			TotalDownlineCount:       int(totalDownlineCount),
			TradingUserCount:         int(tradingUserCount),
			TotalPerpsVolumeUSD:      totalPerpsVolumeUSD,
			TotalPerpsFees:           totalPerpsFees,
			TotalPerpsFeesPaid:       totalPerpsFeesPaid,
			TotalPerpsFeesUnPaid:     totalPerpsFeesUnPaid,
			TotalPerpsTradesCount:    totalPerpsTradesCount,
			TotalMemeVolumeUSD:       totalMemeVolumeUSD,
			TotalMemeFees:            totalMemeFees,
			TotalMemeFeesPaid:        totalMemeFeesPaid,
			TotalMemeFeesUnPaid:      totalMemeFeesUnPaid,
			TotalMemeTradesCount:     totalMemeTradesCount,
			TotalCommissionEarnedUSD: totalCommissionEarnedUSD,
			ClaimedCommissionUSD:     claimedCommissionUSD,
			UnclaimedCommissionUSD:   unclaimedCommissionUSD,
			TotalCashbackEarnedUSD:   totalCashbackEarnedUSD,
			ClaimedCashbackUSD:       claimedCashbackUSD,
			UnclaimedCashbackUSD:     unclaimedCashbackUSD,
			L1UplineID:               l1UplineID,
			L2UplineID:               l2UplineID,
			L3UplineID:               l3UplineID,
		}
		err = global.GVA_DB.Create(&snapshot).Error
		if err != nil {
			return err
		}
		return nil
	} else if err == nil {
		// Update existing snapshot
		updates := model.ReferralSnapshot{
			DirectCount:              int(directCount),
			TotalDownlineCount:       int(totalDownlineCount),
			TradingUserCount:         int(tradingUserCount),
			TotalPerpsVolumeUSD:      totalPerpsVolumeUSD,
			TotalPerpsFees:           totalPerpsFees,
			TotalPerpsFeesPaid:       totalPerpsFeesPaid,
			TotalPerpsFeesUnPaid:     totalPerpsFeesUnPaid,
			TotalPerpsTradesCount:    totalPerpsTradesCount,
			TotalMemeVolumeUSD:       totalMemeVolumeUSD,
			TotalMemeFees:            totalMemeFees,
			TotalMemeFeesPaid:        totalMemeFeesPaid,
			TotalMemeFeesUnPaid:      totalMemeFeesUnPaid,
			TotalMemeTradesCount:     totalMemeTradesCount,
			TotalCommissionEarnedUSD: totalCommissionEarnedUSD,
			ClaimedCommissionUSD:     claimedCommissionUSD,
			UnclaimedCommissionUSD:   unclaimedCommissionUSD,
			TotalCashbackEarnedUSD:   totalCashbackEarnedUSD,
			ClaimedCashbackUSD:       claimedCashbackUSD,
			UnclaimedCashbackUSD:     unclaimedCashbackUSD,
			L1UplineID:               l1UplineID,
			L2UplineID:               l2UplineID,
			L3UplineID:               l3UplineID,
		}
		err = global.GVA_DB.Model(&snapshot).Updates(updates).Error
		if err != nil {
			return err
		}
	} else {
		return err
	}

	return nil
}

// GetPersonalPerpsVolumeUSD 计算个人永续合约总交易量
// 返回指定用户的永续合约交易总量（USD）
func (s *SnapshotTask) GetPersonalPerpsVolumeUSD(userID uuid.UUID) (decimal.Decimal, error) {
	var totalVolume decimal.Decimal

	query := `
		SELECT COALESCE(SUM(
			COALESCE(hlt.avg_price, 0) * COALESCE(CAST(hlt.total_sz AS DECIMAL), 0)
		), 0) as total_amount
		FROM hyper_liquid_transactions hlt
		WHERE hlt.user_id = ?
		AND hlt.status = 'filled'
		AND hlt.avg_price IS NOT NULL
		AND hlt.total_sz IS NOT NULL
	`

	err := global.GVA_DB.Raw(query, userID).Scan(&totalVolume).Error
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate personal perps volume: %w", err)
	}

	return totalVolume, nil
}

// GetPersonalMemeVolumeUSD 计算个人Meme币总交易量
// 返回指定用户在指定时间范围内的Meme币交易总量（USD）
func (s *SnapshotTask) GetPersonalMemeVolumeUSD(userID uuid.UUID) (decimal.Decimal, error) {
	var totalVolume decimal.Decimal

	query := `
		SELECT COALESCE(SUM(volume_usd), 0) as total_amount
		FROM affiliate_transactions
		WHERE user_id = ?
		AND status = 'Completed'
	`

	err := global.GVA_DB.Raw(query, userID).Scan(&totalVolume).Error
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate personal meme volume: %w", err)
	}

	return totalVolume, nil
}

// GetPersonalPerpsFeesPaid 计算个人永续合约已领取的佣金金额
// 返回指定用户的永续合约已领取佣金总额（USD）
func (s *SnapshotTask) GetClaimedCommissionUSD(userID uuid.UUID) (decimal.Decimal, error) {
	var totalFeesPaid decimal.Decimal

	query := `
		SELECT COALESCE(SUM(commission_amount), 0) as total_amount
		FROM commission_ledger
		WHERE recipient_user_id = ?
		AND status = 'CLAIMED'
	`

	err := global.GVA_DB.Raw(query, userID).Scan(&totalFeesPaid).Error
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate personal perps commission claimed: %w", err)
	}

	return totalFeesPaid, nil
}

// GetPersonalPerpsFeesUnPaid 计算个人永续合约未领取的佣金金额
// 返回指定用户的永续合约未领取佣金总额（USD）
func (s *SnapshotTask) GetUnclaimedCommissionUSD(userID uuid.UUID) (decimal.Decimal, error) {
	var totalFeesUnPaid decimal.Decimal

	query := `
		SELECT COALESCE(SUM(commission_amount), 0) as total_amount
		FROM commission_ledger
		WHERE recipient_user_id = ?
		AND status = 'PENDING_CLAIM'
	`

	err := global.GVA_DB.Raw(query, userID).Scan(&totalFeesUnPaid).Error
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate personal perps commission unclaimed: %w", err)
	}

	return totalFeesUnPaid, nil
}

// GetPersonalMemeFeesPaid 计算个人Meme币已领取的返佣金额
// 返回指定用户的Meme币已领取返佣总额（USD）
func (s *SnapshotTask) GetClaimedCashbackUSD(userID uuid.UUID) (decimal.Decimal, error) {
	var totalFeesPaid decimal.Decimal

	query := `
		SELECT COALESCE(SUM(commission_amount), 0) as total_amount
		FROM meme_commission_ledger
		WHERE recipient_user_id = ?
		AND status = 'CLAIMED'
	`

	err := global.GVA_DB.Raw(query, userID).Scan(&totalFeesPaid).Error
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate personal meme cashback claimed: %w", err)
	}

	return totalFeesPaid, nil
}

// GetPersonalMemeFeesUnPaid 计算个人Meme币未领取的返佣金额
// 返回指定用户的Meme币未领取返佣总额（USD）
func (s *SnapshotTask) GetUnclaimedCashbackUSD(userID uuid.UUID) (decimal.Decimal, error) {
	var totalFeesUnPaid decimal.Decimal

	query := `
		SELECT COALESCE(SUM(commission_amount), 0) as total_amount
		FROM meme_commission_ledger
		WHERE recipient_user_id = ?
		AND status = 'PENDING_CLAIM'
	`

	err := global.GVA_DB.Raw(query, userID).Scan(&totalFeesUnPaid).Error
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate personal meme cashback unclaimed: %w", err)
	}

	return totalFeesUnPaid, nil
}

// GetTradingUsersCount 计算有交易记录的用户数量
// 返回指定用户的下线中有交易记录的用户总数（深度 <= 3）
func (s *SnapshotTask) GetTradingUsersCount(userID uuid.UUID) (int64, error) {
	var count int64
	err := global.GVA_DB.Model(&model.User{}).
		Joins("JOIN referrals ON users.id = referrals.user_id").
		Where("referrals.referrer_id = ? AND referrals.depth <= 3", userID).
		Where("users.first_transaction_at IS NOT NULL").
		Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to calculate trading users count: %w", err)
	}
	return count, nil
}

// GetPersonalPerpsTradesCount 计算个人永续合约交易次数
// 返回指定用户的永续合约交易总次数
func (s *SnapshotTask) GetPersonalPerpsTradesCount(userID uuid.UUID) (int64, error) {
	var count int64

	err := global.GVA_DB.Model(&model.HyperLiquidTransaction{}).
		Where("user_id = ? AND status = ?", userID, "filled").
		Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to calculate personal perps trades count: %w", err)
	}

	return count, nil
}

// GetPersonalMemeTradesCount 计算个人Meme币交易次数
// 返回指定用户的Meme币交易总次数
func (s *SnapshotTask) GetPersonalMemeTradesCount(userID uuid.UUID) (int64, error) {
	var count int64

	err := global.GVA_DB.Model(&model.AffiliateTransaction{}).
		Where("user_id = ? AND status = ?", userID, "completed").
		Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to calculate personal meme trades count: %w", err)
	}

	return count, nil
}

// GetTotalPerpsFees 计算个人永续合约总手续费
// 返回指定用户的永续合约手续费总额（USD）
func (s *SnapshotTask) GetTotalPerpsFees(userID uuid.UUID) (decimal.Decimal, error) {
	var totalFees decimal.Decimal

	query := `
		SELECT COALESCE(SUM(hlt.build_fee), 0) as total_amount
		FROM hyper_liquid_transactions hlt
		WHERE hlt.user_id = ?
		AND hlt.status = 'filled'
		AND hlt.build_fee IS NOT NULL
	`

	err := global.GVA_DB.Raw(query, userID).Scan(&totalFees).Error
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate total perps fees: %w", err)
	}

	return totalFees, nil
}

// GetTotalMemeFees 计算个人Meme币总手续费
// 返回指定用户的Meme币手续费总额（USD）
func (s *SnapshotTask) GetTotalMemeFees(userID uuid.UUID) (decimal.Decimal, error) {
	var totalFees decimal.Decimal

	query := `
		SELECT COALESCE(SUM(at.platform_fee), 0) as total_amount
		FROM affiliate_transactions at
		WHERE at.user_id = ?
		AND at.status = 'Completed'
		AND at.platform_fee IS NOT NULL
	`

	err := global.GVA_DB.Raw(query, userID).Scan(&totalFees).Error
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to calculate total meme fees: %w", err)
	}

	return totalFees, nil
}
