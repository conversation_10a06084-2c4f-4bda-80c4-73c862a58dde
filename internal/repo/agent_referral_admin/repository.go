package agent_referral_admin

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)


// AdminAgentReferralRepositoryInterface defines the repository interface for admin operations
type AdminAgentReferralRepositoryInterface interface {
	// Infinite Agent Operations
	CreateInfiniteAgent(ctx context.Context, agent *model.InfiniteAgentConfig) error
	GetInfiniteAgentById(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentConfig, error)
	GetAllInfiniteAgents(ctx context.Context, offset, limit int, status, sortBy, sortOrder string) ([]*model.InfiniteAgentConfig, int, error)
	UpdateInfiniteAgent(ctx context.Context, agent *model.InfiniteAgentConfig) error
	DeleteInfiniteAgent(ctx context.Context, id uuid.UUID) error

	// Agent Level Operations
	GetAllAgentLevels(ctx context.Context) ([]*model.AgentLevel, error)
	UpdateAgentLevel(ctx context.Context, level *model.AgentLevel) error
	GetAgentLevelStats(ctx context.Context, levelID int) (int, float64, float64, error)

	// User Operations
	SearchUsersByInvitationCode(ctx context.Context, invitationCode string) ([]*model.User, error)
	GetUsersByAgentLevel(ctx context.Context, levelID, offset, limit int, sortBy, sortOrder string) ([]*model.User, int, error)

	// Referral Tree Operations
	GetAllReferralTrees(ctx context.Context, offset, limit int, sortBy, sortOrder string) ([]*model.InfiniteAgentReferralTree, int, error)
	CreateReferralTree(ctx context.Context, tree *model.InfiniteAgentReferralTree) error
	GetReferralTreeById(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentReferralTree, error)
	DeleteReferralTree(ctx context.Context, id uuid.UUID) error

	// Statistics Operations
	GetAgentReferralStats(ctx context.Context, startDate, endDate time.Time) (map[string]interface{}, error)
	GetCommissionDistributionStats(ctx context.Context, startDate, endDate time.Time) (map[string]float64, error)
	GetTopPerformingAgents(ctx context.Context, limit int, sortBy string) ([]*model.User, error)
}

// AdminAgentReferralRepository implements the repository interface
type AdminAgentReferralRepository struct{}

// NewAdminAgentReferralRepository creates a new repository instance
func NewAdminAgentReferralRepository() AdminAgentReferralRepositoryInterface {
	return &AdminAgentReferralRepository{}
}

// Infinite Agent Operations

func (r *AdminAgentReferralRepository) CreateInfiniteAgent(ctx context.Context, agent *model.InfiniteAgentConfig) error {
	return global.GVA_DB.WithContext(ctx).Create(agent).Error
}

func (r *AdminAgentReferralRepository) GetInfiniteAgentById(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentConfig, error) {
	var agent model.InfiniteAgentConfig
	err := global.GVA_DB.WithContext(ctx).
		Preload("User").
		First(&agent, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &agent, nil
}

func (r *AdminAgentReferralRepository) GetAllInfiniteAgents(ctx context.Context, offset, limit int, status, sortBy, sortOrder string) ([]*model.InfiniteAgentConfig, int, error) {
	var agents []*model.InfiniteAgentConfig
	var total int64

	query := global.GVA_DB.WithContext(ctx).Model(&model.InfiniteAgentConfig{}).Preload("User")

	// Apply status filter
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	if sortBy != "" {
		orderClause := fmt.Sprintf("%s %s", sortBy, sortOrder)
		query = query.Order(orderClause)
	} else {
		query = query.Order("created_at DESC")
	}

	// Apply pagination
	if err := query.Offset(offset).Limit(limit).Find(&agents).Error; err != nil {
		return nil, 0, err
	}

	return agents, int(total), nil
}

func (r *AdminAgentReferralRepository) UpdateInfiniteAgent(ctx context.Context, agent *model.InfiniteAgentConfig) error {
	return global.GVA_DB.WithContext(ctx).Save(agent).Error
}

func (r *AdminAgentReferralRepository) DeleteInfiniteAgent(ctx context.Context, id uuid.UUID) error {
	return global.GVA_DB.WithContext(ctx).Delete(&model.InfiniteAgentConfig{}, "id = ?", id).Error
}

// Agent Level Operations

func (r *AdminAgentReferralRepository) GetAllAgentLevels(ctx context.Context) ([]*model.AgentLevel, error) {
	var levels []*model.AgentLevel
	err := global.GVA_DB.WithContext(ctx).Find(&levels).Error
	return levels, err
}

func (r *AdminAgentReferralRepository) UpdateAgentLevel(ctx context.Context, level *model.AgentLevel) error {
	return global.GVA_DB.WithContext(ctx).Save(level).Error
}

func (r *AdminAgentReferralRepository) GetAgentLevelStats(ctx context.Context, levelID int) (int, float64, float64, error) {
	var userCount int64

	// Get user count for this level
	err := global.GVA_DB.WithContext(ctx).
		Model(&model.User{}).
		Where("agent_level_id = ?", levelID).
		Count(&userCount).Error
	if err != nil {
		return 0, 0, 0, err
	}

	// Get total volume and commission from referral snapshots
	var result struct {
		TotalVolume     float64
		TotalCommission float64
	}

	err = global.GVA_DB.WithContext(ctx).
		Table("referral_snapshots rs").
		Select("COALESCE(SUM(rs.total_perps_volume_usd + rs.total_meme_volume_usd), 0) as total_volume, COALESCE(SUM(rs.total_commission_earned_usd), 0) as total_commission").
		Joins("JOIN users u ON rs.user_id = u.id").
		Where("u.agent_level_id = ?", levelID).
		Scan(&result).Error

	if err != nil {
		return int(userCount), 0, 0, err
	}

	return int(userCount), result.TotalVolume, result.TotalCommission, nil
}

// User Operations

func (r *AdminAgentReferralRepository) SearchUsersByInvitationCode(ctx context.Context, invitationCode string) ([]*model.User, error) {
	var users []*model.User
	err := global.GVA_DB.WithContext(ctx).
		Preload("AgentLevel").
		Preload("ReferralSnapshot").
		Where("invitation_code ILIKE ?", "%"+invitationCode+"%").
		Find(&users).Error
	return users, err
}

func (r *AdminAgentReferralRepository) GetUsersByAgentLevel(ctx context.Context, levelID, offset, limit int, sortBy, sortOrder string) ([]*model.User, int, error) {
	var users []*model.User
	var total int64

	query := global.GVA_DB.WithContext(ctx).
		Model(&model.User{}).
		Preload("AgentLevel").
		Preload("ReferralSnapshot").
		Where("agent_level_id = ?", levelID)

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	if sortBy != "" {
		orderClause := fmt.Sprintf("%s %s", sortBy, sortOrder)
		query = query.Order(orderClause)
	} else {
		query = query.Order("created_at DESC")
	}

	// Apply pagination
	if err := query.Offset(offset).Limit(limit).Find(&users).Error; err != nil {
		return nil, 0, err
	}

	return users, int(total), nil
}

// Referral Tree Operations

func (r *AdminAgentReferralRepository) GetAllReferralTrees(ctx context.Context, offset, limit int, sortBy, sortOrder string) ([]*model.InfiniteAgentReferralTree, int, error) {
	var trees []*model.InfiniteAgentReferralTree
	var total int64

	query := global.GVA_DB.WithContext(ctx).Model(&model.InfiniteAgentReferralTree{})

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	if sortBy != "" {
		orderClause := fmt.Sprintf("%s %s", sortBy, sortOrder)
		query = query.Order(orderClause)
	} else {
		query = query.Order("created_at DESC")
	}

	// Apply pagination
	if err := query.Offset(offset).Limit(limit).Find(&trees).Error; err != nil {
		return nil, 0, err
	}

	return trees, int(total), nil
}

func (r *AdminAgentReferralRepository) CreateReferralTree(ctx context.Context, tree *model.InfiniteAgentReferralTree) error {
	return global.GVA_DB.WithContext(ctx).Create(tree).Error
}

func (r *AdminAgentReferralRepository) GetReferralTreeById(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentReferralTree, error) {
	var tree model.InfiniteAgentReferralTree
	err := global.GVA_DB.WithContext(ctx).First(&tree, "id = ?", id).Error
	if err != nil {
		return nil, err
	}
	return &tree, nil
}

func (r *AdminAgentReferralRepository) DeleteReferralTree(ctx context.Context, id uuid.UUID) error {
	return global.GVA_DB.WithContext(ctx).Delete(&model.InfiniteAgentReferralTree{}, "id = ?", id).Error
}

// Statistics Operations

func (r *AdminAgentReferralRepository) GetAgentReferralStats(ctx context.Context, startDate, endDate time.Time) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total users
	var totalUsers int64
	global.GVA_DB.WithContext(ctx).Model(&model.User{}).Count(&totalUsers)
	stats["total_users"] = totalUsers

	// Total infinite agents
	var totalInfiniteAgents int64
	global.GVA_DB.WithContext(ctx).Model(&model.InfiniteAgentConfig{}).Where("status = ?", "ACTIVE").Count(&totalInfiniteAgents)
	stats["total_infinite_agents"] = totalInfiniteAgents

	// Total volume and commission
	var result struct {
		TotalVolume     float64
		TotalCommission float64
	}
	global.GVA_DB.WithContext(ctx).
		Table("referral_snapshots").
		Select("COALESCE(SUM(total_perps_volume_usd + total_meme_volume_usd), 0) as total_volume, COALESCE(SUM(total_commission_earned_usd), 0) as total_commission").
		Scan(&result)

	stats["total_volume_usd"] = result.TotalVolume
	stats["total_commission_paid"] = result.TotalCommission

	// Active referral trees
	var activeReferralTrees int64
	global.GVA_DB.WithContext(ctx).Model(&model.InfiniteAgentReferralTree{}).Count(&activeReferralTrees)
	stats["active_referral_trees"] = activeReferralTrees

	// Average tree size
	var avgTreeSize float64
	global.GVA_DB.WithContext(ctx).
		Table("infinite_agent_referral_trees").
		Select("COALESCE(AVG(total_nodes), 0)").
		Scan(&avgTreeSize)
	stats["average_tree_size"] = avgTreeSize

	return stats, nil
}

func (r *AdminAgentReferralRepository) GetCommissionDistributionStats(ctx context.Context, startDate, endDate time.Time) (map[string]float64, error) {
	stats := make(map[string]float64)

	// Get commission distribution from commission ledger
	var result struct {
		DirectCommission   float64
		IndirectCommission float64
		ExtendedCommission float64
		InfiniteCommission float64
	}

	err := global.GVA_DB.WithContext(ctx).
		Table("commission_ledger").
		Select(`
			COALESCE(SUM(CASE WHEN commission_type = 'DIRECT' THEN amount_usd ELSE 0 END), 0) as direct_commission,
			COALESCE(SUM(CASE WHEN commission_type = 'INDIRECT' THEN amount_usd ELSE 0 END), 0) as indirect_commission,
			COALESCE(SUM(CASE WHEN commission_type = 'EXTENDED' THEN amount_usd ELSE 0 END), 0) as extended_commission,
			COALESCE(SUM(CASE WHEN commission_type = 'INFINITE' THEN amount_usd ELSE 0 END), 0) as infinite_commission
		`).
		Where("created_at BETWEEN ? AND ?", startDate, endDate).
		Scan(&result).Error

	if err != nil {
		return stats, err
	}

	stats["direct_commission"] = result.DirectCommission
	stats["indirect_commission"] = result.IndirectCommission
	stats["extended_commission"] = result.ExtendedCommission
	stats["infinite_commission"] = result.InfiniteCommission
	stats["total_commission"] = result.DirectCommission + result.IndirectCommission + result.ExtendedCommission + result.InfiniteCommission

	return stats, nil
}

func (r *AdminAgentReferralRepository) GetTopPerformingAgents(ctx context.Context, limit int, sortBy string) ([]*model.User, error) {
	var users []*model.User

	query := global.GVA_DB.WithContext(ctx).
		Preload("AgentLevel").
		Preload("ReferralSnapshot")

	// Apply sorting based on sortBy parameter
	switch sortBy {
	case "COMMISSION_EARNED":
		query = query.
			Joins("LEFT JOIN referral_snapshots rs ON users.id = rs.user_id").
			Order("rs.total_commission_earned_usd DESC")
	case "VOLUME_GENERATED":
		query = query.
			Joins("LEFT JOIN referral_snapshots rs ON users.id = rs.user_id").
			Order("(rs.total_perps_volume_usd + rs.total_meme_volume_usd) DESC")
	case "REFERRAL_COUNT":
		query = query.
			Joins("LEFT JOIN referral_snapshots rs ON users.id = rs.user_id").
			Order("rs.total_downline_count DESC")
	default:
		query = query.
			Joins("LEFT JOIN referral_snapshots rs ON users.id = rs.user_id").
			Order("rs.total_commission_earned_usd DESC")
	}

	err := query.Limit(limit).Find(&users).Error
	return users, err
}
