package initializer

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	repoAgentReferral "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/agent_referral"
	task2 "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task/agent_referral"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task/infinite"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task/level"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task/volume"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"

	"go.uber.org/zap"
)

func InitTask() {
	scheduler := task2.NewTaskScheduler()
	ctx, cancel := context.WithCancel(context.Background())
	scheduler.SetCancelFunc(cancel)
	go scheduler.RunWithSignal(ctx)

	agentReferralTask := agent_referral.NewSnapshotTask(&repoAgentReferral.InvitationRepository{})
	err := scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskAgentReferralSnapshot].ID, global.GVA_CONFIG.CronTasks[utils.TaskAgentReferralSnapshot].Cron, agentReferralTask.UpdateAllReferralSnapshots)
	if err != nil {
		global.GVA_LOG.Error("task registration failed", zap.Any("err", err))
	}

	levelUpgradeTask := level.NewLevelUpgradeTask()
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskLevelUpgrade].ID, global.GVA_CONFIG.CronTasks[utils.TaskLevelUpgrade].Cron, levelUpgradeTask.UpgradeUserLevels)
	if err != nil {
		global.GVA_LOG.Error("level upgrade task registration failed", zap.Any("err", err))
	}

	referralTreeSnapshotTask := infinite.NewReferralTreeSnapshotTask()
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskReferralTreeSnapshot].ID, global.GVA_CONFIG.CronTasks[utils.TaskReferralTreeSnapshot].Cron, referralTreeSnapshotTask.CreateReferralTreeSnapshots)
	if err != nil {
		global.GVA_LOG.Error("referral tree snapshot task registration failed", zap.Any("err", err))
	}

	// Register Activity Cashback scheduled tasks
	activityCashbackTasks := activity_cashback.NewActivityCashbackScheduledTasks()

	// Daily task reset
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskActivityCashbackDailyReset].ID, global.GVA_CONFIG.CronTasks[utils.TaskActivityCashbackDailyReset].Cron, activityCashbackTasks.DailyTaskReset)
	if err != nil {
		global.GVA_LOG.Error("activity cashback daily reset task registration failed", zap.Any("err", err))
	}

	// Tier upgrade check
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskActivityCashbackTierUpgrade].ID, global.GVA_CONFIG.CronTasks[utils.TaskActivityCashbackTierUpgrade].Cron, activityCashbackTasks.TierUpgradeCheck)
	if err != nil {
		global.GVA_LOG.Error("activity cashback tier upgrade task registration failed", zap.Any("err", err))
	}

	// Cashback processing
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskActivityCashbackProcessing].ID, global.GVA_CONFIG.CronTasks[utils.TaskActivityCashbackProcessing].Cron, activityCashbackTasks.CashbackProcessing)
	if err != nil {
		global.GVA_LOG.Error("activity cashback processing task registration failed", zap.Any("err", err))
	}

	// Monthly stats reset
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskActivityCashbackMonthlyReset].ID, global.GVA_CONFIG.CronTasks[utils.TaskActivityCashbackMonthlyReset].Cron, activityCashbackTasks.MonthlyStatsReset)
	if err != nil {
		global.GVA_LOG.Error("activity cashback monthly reset task registration failed", zap.Any("err", err))
	}

	// Task progress cleanup
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskActivityCashbackCleanup].ID, global.GVA_CONFIG.CronTasks[utils.TaskActivityCashbackCleanup].Cron, activityCashbackTasks.TaskProgressCleanup)
	if err != nil {
		global.GVA_LOG.Error("activity cashback cleanup task registration failed", zap.Any("err", err))
	}

	// Community tasks processing
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskActivityCashbackCommunityTasks].ID, global.GVA_CONFIG.CronTasks[utils.TaskActivityCashbackCommunityTasks].Cron, activityCashbackTasks.CommunityTasksProcessing)
	if err != nil {
		global.GVA_LOG.Error("activity cashback community tasks processing task registration failed", zap.Any("err", err))
	}

	// Consecutive check-in streak monitoring
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskActivityCashbackStreakMonitoring].ID, global.GVA_CONFIG.CronTasks[utils.TaskActivityCashbackStreakMonitoring].Cron, activityCashbackTasks.ConsecutiveCheckinStreakMonitoring)
	if err != nil {
		global.GVA_LOG.Error("activity cashback streak monitoring task registration failed", zap.Any("err", err))
	}

	infiniteAgentReferralTreeTask := infinite.NewInfiniteAgentReferralTreeTask()
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskInfiniteAgentReferralTree].ID, global.GVA_CONFIG.CronTasks[utils.TaskInfiniteAgentReferralTree].Cron, infiniteAgentReferralTreeTask.CreateInfiniteAgentReferralTrees)
	if err != nil {
		global.GVA_LOG.Error("infinite agent referral tree task registration failed", zap.Any("err", err))
	}

	infiniteAgentCommissionTask := infinite.NewInfiniteAgentCommissionTask()
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskInfiniteAgentCommission].ID, global.GVA_CONFIG.CronTasks[utils.TaskInfiniteAgentCommission].Cron, infiniteAgentCommissionTask.CalculateInfiniteAgentCommissions)
	if err != nil {
		global.GVA_LOG.Error("infinite agent commission task registration failed", zap.Any("err", err))
	}

	// Register Realtime Volume Sync Task
	realtimeVolumeSyncTask := volume.NewRealtimeVolumeSyncTask()
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskRealtimeVolumeSync].ID, global.GVA_CONFIG.CronTasks[utils.TaskRealtimeVolumeSync].Cron, realtimeVolumeSyncTask.SyncTradingVolumes)
	if err != nil {
		global.GVA_LOG.Error("realtime volume sync task registration failed", zap.Any("err", err))
	}

	// Register Transaction Aggregation Task
	transactionAggregationTask := level.NewTransactionAggregationTask()
	err = scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskTransactionAggregation].ID, global.GVA_CONFIG.CronTasks[utils.TaskTransactionAggregation].Cron, transactionAggregationTask.AggregateTransactionData)
	if err != nil {
		global.GVA_LOG.Error("transaction aggregation task registration failed", zap.Any("err", err))
	}

}
