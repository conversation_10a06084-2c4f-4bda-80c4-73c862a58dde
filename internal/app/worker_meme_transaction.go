package app

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/nats-io/nats.go"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task"

	natsClient "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
)

func InitializeMemeTransactionWorker() {
	global.GVA_VP = initializer.Viper()
	global.GVA_LOG = initializer.Zap()
	zap.ReplaceGlobals(global.GVA_LOG)
	global.GVA_DB = initializer.Gorm()
	initializer.DBList()

	initializer.InitNatsMeme()

	memeNats := global.GVA_NATS_MEME

	// Ensure the affiliate stream exists
	_, err := memeNats.AddStream(&nats.StreamConfig{
		Name:     natsClient.AffiliateStream,
		Subjects: []string{natsClient.AffiliateTxSubject, natsClient.SolPriceSubject},
		Storage:  nats.FileStorage,
	})
	if err != nil {
		panic(fmt.Sprintf("Failed to ensure affiliate stream %s exists: %v", natsClient.AffiliateStream, err))
	}

	// Start both affiliate transaction and SOL price workers
	go runMemeAffiliateTxWorker()
	go runMemeSolPriceWorker()

	select {}
}

func runMemeAffiliateTxWorker() error {
	logger := global.GVA_LOG

	nc := global.GVA_NATS_MEME

	subject := natsClient.AffiliateTxSubject
	consumerName := natsClient.AffiliateTxConsumer
	streamName := natsClient.AffiliateStream

	// Check if consumer exists and delete if it's push-based
	consumerInfo, err := nc.ConsumerInfo(streamName, consumerName)
	if err == nil && consumerInfo != nil {
		// If consumer exists but is push-based, delete it to recreate as pull-based
		if consumerInfo.Config.DeliverSubject != "" {
			logger.Info("Deleting existing push-based consumer to recreate as pull-based",
				zap.String("consumer", consumerName),
				zap.String("stream", streamName))

			if deleteErr := nc.DeleteConsumer(streamName, consumerName); deleteErr != nil {
				logger.Error("Failed to delete existing push-based consumer",
					zap.String("consumer", consumerName),
					zap.Error(deleteErr))
				return deleteErr
			}
			logger.Info("Successfully deleted push-based consumer", zap.String("consumer", consumerName))
		}
	}

	opts := []nats.SubOpt{
		nats.BindStream(streamName),
		nats.ManualAck(),
		nats.MaxDeliver(10),
		nats.BackOff([]time.Duration{500 * time.Millisecond, time.Second, 3 * time.Second, 5 * time.Second}),
	}
	jsConsumer, err := nc.PullSubscribe(subject, consumerName, opts...)
	if err != nil {
		logger.Error("Could not subscribe to affiliate tx subject", zap.String("Subject", subject), zap.Error(err))
		return err
	}

	_, cancel := context.WithCancel(context.Background())
	defer cancel()

	logger.Info("Started Meme Affiliate Transaction Worker", zap.String("subject", subject))

	for {
		startTime := time.Now()
		msgs, err := jsConsumer.Fetch(100, nats.MaxWait(2*time.Second))

		if errors.Is(err, nats.ErrTimeout) {
			logger.Debug("No affiliate transaction messages available at the moment")
			continue
		}

		if err != nil {
			logger.Error("failed to fetch affiliate tx msgs:", zap.Error(err))
			continue
		}

		if len(msgs) == 0 {
			logger.Debug("No affiliate transaction messages fetched", zap.String("Subject", subject))
			continue
		}

		err = task.ConsumeMemeAffiliateTxEvent(msgs)

		for _, m := range msgs {
			if err == nil {
				if ackErr := m.Ack(); ackErr != nil {
					logger.Error("failed to ack affiliate tx message", zap.Error(ackErr))
				}
			} else {
				if nakErr := m.Nak(); nakErr != nil {
					logger.Error("failed to nak affiliate tx message", zap.Error(nakErr))
				}
			}
		}

		processingTime := time.Since(startTime)
		logger.Info("Processed affiliate transaction batch",
			zap.Int("message_count", len(msgs)),
			zap.Duration("processing_time", processingTime),
			zap.Bool("success", err == nil))
	}
}

func runMemeSolPriceWorker() error {
	logger := global.GVA_LOG

	nc := global.GVA_NATS_MEME

	subject := natsClient.SolPriceSubject
	consumerName := natsClient.SolPriceConsumer
	streamName := natsClient.AffiliateStream

	// Check if consumer exists and delete if it's push-based
	consumerInfo, err := nc.ConsumerInfo(streamName, consumerName)
	if err == nil && consumerInfo != nil {
		// If consumer exists but is push-based, delete it to recreate as pull-based
		if consumerInfo.Config.DeliverSubject != "" {
			logger.Info("Deleting existing push-based consumer to recreate as pull-based",
				zap.String("consumer", consumerName),
				zap.String("stream", streamName))

			if deleteErr := nc.DeleteConsumer(streamName, consumerName); deleteErr != nil {
				logger.Error("Failed to delete existing push-based consumer",
					zap.String("consumer", consumerName),
					zap.Error(deleteErr))
				return deleteErr
			}
			logger.Info("Successfully deleted push-based consumer", zap.String("consumer", consumerName))
		}
	}

	opts := []nats.SubOpt{
		nats.BindStream(streamName),
		nats.ManualAck(),
		nats.MaxDeliver(10),
		nats.BackOff([]time.Duration{500 * time.Millisecond, time.Second, 3 * time.Second, 5 * time.Second}),
	}
	jsConsumer, err := nc.PullSubscribe(subject, consumerName, opts...)
	if err != nil {
		logger.Error("Could not subscribe to SOL price subject", zap.String("Subject", subject), zap.Error(err))
		return err
	}

	_, cancel := context.WithCancel(context.Background())
	defer cancel()

	logger.Info("Started Meme SOL Price Worker", zap.String("subject", subject))

	// throttle info log for processed batch to once every 20 seconds
	lastSolPriceLog := time.Now()

	for {
		startTime := time.Now()
		msgs, err := jsConsumer.Fetch(100, nats.MaxWait(2*time.Second))

		if errors.Is(err, nats.ErrTimeout) {
			logger.Debug("No SOL price messages available at the moment")
			continue
		}

		if err != nil {
			logger.Error("failed to fetch SOL price msgs:", zap.Error(err))
			continue
		}

		if len(msgs) == 0 {
			logger.Debug("No SOL price messages fetched", zap.String("Subject", subject))
			continue
		}

		err = task.ConsumeMemeSolPriceEvent(msgs)

		for _, m := range msgs {
			if err == nil {
				if ackErr := m.Ack(); ackErr != nil {
					logger.Error("failed to ack SOL price message", zap.Error(ackErr))
				}
			} else {
				if nakErr := m.Nak(); nakErr != nil {
					logger.Error("failed to nak SOL price message", zap.Error(nakErr))
				}
			}
		}

		processingTime := time.Since(startTime)
		if time.Since(lastSolPriceLog) >= 30*time.Second {
			logger.Info("Processed SOL price batch",
				zap.Int("message_count", len(msgs)),
				zap.Duration("processing_time", processingTime),
				zap.Bool("success", err == nil))
			lastSolPriceLog = time.Now()
		}
	}
}
